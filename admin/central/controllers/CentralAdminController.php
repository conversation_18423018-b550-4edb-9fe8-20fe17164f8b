<?php

/**
 * Central admin dashboard controller
 * Handles main dashboard for super admin
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class CentralAdminController
{
    private $auth;
    private $tenantCreator;

    public function __construct()
    {
        $this->auth = new Auth();
        $this->tenantCreator = new TenantCreator();
        
        // Require super admin authentication
        $this->auth->requireSuperAdmin();
    }

    /**
     * Show dashboard
     */
    public function dashboard()
    {
        $data = [
            'title' => 'Central Admin Dashboard',
            'user' => $this->auth->getSuperAdmin(),
            'stats' => $this->getDashboardStats()
        ];
        
        $this->render('dashboard', $data);
    }

    /**
     * Get dashboard statistics
     * 
     * @return array
     */
    private function getDashboardStats(): array
    {
        $tenants = $this->tenantCreator->getAllTenants();
        
        $stats = [
            'total_tenants' => count($tenants),
            'active_tenants' => count(array_filter($tenants, fn($t) => $t['status'] === 'active')),
            'inactive_tenants' => count(array_filter($tenants, fn($t) => $t['status'] === 'inactive')),
            'suspended_tenants' => count(array_filter($tenants, fn($t) => $t['status'] === 'suspended')),
            'recent_tenants' => array_slice($tenants, 0, 5)
        ];
        
        return $stats;
    }

    /**
     * Render view
     * 
     * @param string $view
     * @param array $data
     */
    private function render(string $view, array $data = [])
    {
        extract($data);
        
        ob_start();
        include __DIR__ . "/../views/{$view}.php";
        $content = ob_get_clean();
        
        include __DIR__ . '/../views/layout.php';
    }
}