<?php

/**
 * Central admin tenant management controller
 * Handles tenant CRUD operations
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class CentralAdminTenantController
{
    private $auth;
    private $tenantCreator;

    public function __construct()
    {
        $this->auth = new Auth();
        $this->tenantCreator = new TenantCreator();
        
        // Require super admin authentication
        $this->auth->requireSuperAdmin();
    }

    /**
     * List all tenants
     */
    public function index()
    {
        $tenants = $this->tenantCreator->getAllTenants();
        
        $data = [
            'title' => 'Tenant Management',
            'tenants' => $tenants,
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];
        
        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);
        
        $this->render('tenants/index', $data);
    }

    /**
     * Show create tenant form
     */
    public function create()
    {
        $themes = $this->tenantCreator->getAvailableThemes();
        
        $data = [
            'title' => 'Create New Tenant',
            'themes' => $themes,
            'error' => $_SESSION['error'] ?? null,
            'errors' => $_SESSION['errors'] ?? null
        ];
        
        unset($_SESSION['error'], $_SESSION['errors']);
        
        $this->render('tenants/create', $data);
    }

    /**
     * Store new tenant
     */
    public function store()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/central/tenants');
        }

        // Verify CSRF token
        try {
            $this->auth->requireCsrfToken();
        } catch (Exception $e) {
            $_SESSION['errors'] = ['Security token mismatch. Please try again.'];
            $this->redirect('/central/tenants/create');
        }

        $domain = sanitize($_POST['domain'] ?? '');
        $theme = sanitize($_POST['theme'] ?? '');
        $status = sanitize($_POST['status'] ?? 'active');
        $expiredAt = !empty($_POST['expired_at']) ? $_POST['expired_at'] : null;
        $expiredAt = !empty($_POST['expired_at']) ? $_POST['expired_at'] : null;

        // Validation
        $errors = [];
        
        if (empty($domain)) {
            $errors[] = 'Domain gereklidir.';
        }
        
        if (empty($theme)) {
            $errors[] = 'Tema seçimi gereklidir.';
        }

        // Check if domain already exists
        if (!empty($domain)) {
            $tenants = $this->tenantCreator->getAllTenants();
            foreach ($tenants as $tenant) {
                if ($tenant['domain'] === $domain) {
                    $errors[] = 'Bu domain zaten kullanılıyor.';
                    break;
                }
            }
        }

        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $this->redirect('/central/tenants/create');
        }

        try {
            $result = $this->tenantCreator->createTenant([
                'domain' => $domain,
                'theme' => $theme,
                'status' => $status
            ], null, $expiredAt);

            $_SESSION['success'] = "Tenant '{$domain}' başarıyla oluşturuldu!";
            $this->redirect('/central/tenants');

        } catch (Exception $e) {
            $_SESSION['errors'] = ['Tenant oluşturulurken hata: ' . $e->getMessage()];
            $this->redirect('/central/tenants/create');
        }
    }

    /**
     * Show edit tenant form
     */
    public function edit($id)
    {
        $tenants = $this->tenantCreator->getAllTenants();
        $tenant = array_filter($tenants, fn($t) => $t['id'] == $id);
        
        if (empty($tenant)) {
            $_SESSION['error'] = 'Tenant bulunamadı.';
            $this->redirect('/central/tenants');
        }

        $tenant = array_values($tenant)[0];
        
        // Ensure all required fields exist
        $tenant['domain'] = $tenant['domain'] ?? '';
        $tenant['theme'] = $tenant['theme'] ?? $tenant['theme_name'] ?? '';
        $tenant['status'] = $tenant['status'] ?? 'active';
        
        $data = [
            'title' => 'Tenant Düzenle',
            'tenant' => $tenant,
            'errors' => $_SESSION['errors'] ?? null
        ];
        
        unset($_SESSION['errors']);
        
        $this->render('tenants/edit', $data);
    }

    /**
     * Update tenant
     */
    public function update($id)
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/central/tenants');
        }

        // Verify CSRF token
        try {
            $this->auth->requireCsrfToken();
        } catch (Exception $e) {
            $_SESSION['errors'] = ['Security token mismatch. Please try again.'];
            $this->redirect('/central/tenants/' . $id . '/edit');
        }

        $tenants = $this->tenantCreator->getAllTenants();
        $tenant = array_filter($tenants, fn($t) => $t['id'] == $id);
        
        if (empty($tenant)) {
            $_SESSION['error'] = 'Tenant bulunamadı.';
            $this->redirect('/central/tenants');
        }

        $tenant = array_values($tenant)[0];

        $domain = sanitize($_POST['domain'] ?? '');
        $theme = sanitize($_POST['theme'] ?? '');
        $status = sanitize($_POST['status'] ?? 'active');
        $expiredAt = !empty($_POST['expired_at']) ? $_POST['expired_at'] : null;
        $expiredAt = !empty($_POST['expired_at']) ? $_POST['expired_at'] : null;

        // Validation
        $errors = [];
        
        if (empty($domain)) {
            $errors[] = 'Domain gereklidir.';
        }
        
        if (empty($theme)) {
            $errors[] = 'Tema seçimi gereklidir.';
        }

        // Check if domain already exists (except current tenant)
        if (!empty($domain) && $domain !== $tenant['domain']) {
            foreach ($tenants as $t) {
                if ($t['domain'] === $domain && $t['id'] != $id) {
                    $errors[] = 'Bu domain zaten kullanılıyor.';
                    break;
                }
            }
        }

        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $this->redirect('/central/tenants/' . $id . '/edit');
        }

        try {
            $result = $this->tenantCreator->updateTenant($id, [
                'domain' => $domain,
                'theme' => $theme,
                'status' => $status,
                'expired_at' => $expiredAt
            ]);

            $_SESSION['success'] = "Tenant '{$domain}' başarıyla güncellendi!";
            $this->redirect('/central/tenants');

        } catch (Exception $e) {
            $_SESSION['error'] = 'Tenant güncellenirken hata: ' . $e->getMessage();
            $this->redirect('/central/tenants/' . $id . '/edit');
        }
    }

    /**
     * Delete tenant
     */
    public function delete($id)
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/central/tenants');
        }

        try {
            $this->auth->requireCsrfToken();
        } catch (Exception $e) {
            $_SESSION['error'] = 'Security token mismatch.';
            $this->redirect('/central/tenants');
        }

        // Get tenant info first
        $tenants = $this->tenantCreator->getAllTenants();
        $tenant = array_filter($tenants, fn($t) => $t['id'] == $id);
        
        if (empty($tenant)) {
            $_SESSION['error'] = 'Tenant not found.';
            $this->redirect('/central/tenants');
        }

        $tenant = array_values($tenant)[0];

        try {
            $this->tenantCreator->deleteTenant($tenant['domain']);
            $_SESSION['success'] = "Tenant '{$tenant['domain']}' deleted successfully!";
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to delete tenant: ' . $e->getMessage();
        }

        $this->redirect('/central/tenants');
    }

    /**
     * Toggle tenant status
     */
    public function toggleStatus($id)
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/central/tenants');
        }

        try {
            $this->auth->requireCsrfToken();
        } catch (Exception $e) {
            $_SESSION['error'] = 'Security token mismatch.';
            $this->redirect('/central/tenants');
        }

        // Get tenant info
        $tenants = $this->tenantCreator->getAllTenants();
        $tenant = array_filter($tenants, fn($t) => $t['id'] == $id);
        
        if (empty($tenant)) {
            $_SESSION['error'] = 'Tenant not found.';
            $this->redirect('/central/tenants');
        }

        $tenant = array_values($tenant)[0];
        $newStatus = $tenant['status'] === 'active' ? 'inactive' : 'active';

        try {
            $this->tenantCreator->updateTenantStatus($tenant['domain'], $newStatus);
            $_SESSION['success'] = "Tenant status updated to '{$newStatus}'.";
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update tenant status: ' . $e->getMessage();
        }

        $this->redirect('/central/tenants');
    }

    /**
     * Render view
     * 
     * @param string $view
     * @param array $data
     */
    private function render(string $view, array $data = [])
    {
        extract($data);
        
        ob_start();
        include __DIR__ . "/../views/{$view}.php";
        $content = ob_get_clean();
        
        include __DIR__ . '/../views/layout.php';
    }

    /**
     * Redirect to URL
     * 
     * @param string $url
     */
    private function redirect(string $url)
    {
        header("Location: {$url}");
        exit;
    }
}