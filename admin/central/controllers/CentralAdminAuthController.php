<?php

/**
 * Central admin authentication controller
 * Handles login/logout for super admin
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class CentralAdminAuthController
{
    private $auth;

    public function __construct()
    {
        $this->auth = new Auth();
    }

    /**
     * Show login form
     */
    public function loginForm()
    {
        // If already logged in, redirect to dashboard
        if ($this->auth->isSuperAdmin()) {
            $this->redirect('/central-admin/dashboard');
        }

        $data = [
            'title' => 'Central Admin Login',
            'error' => $_SESSION['login_error'] ?? null
        ];
        
        // Clear error message
        unset($_SESSION['login_error']);
        
        $this->render('login', $data);
    }

    /**
     * Process login
     */
    public function login()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/central-admin/login');
        }

        // Verify CSRF token
        try {
            $this->auth->requireCsrfToken();
        } catch (Exception $e) {
            $_SESSION['login_error'] = 'Security token mismatch. Please try again.';
            $this->redirect('/central-admin/login');
        }

        $username = sanitize($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';

        if (empty($username) || empty($password)) {
            $_SESSION['login_error'] = 'Username and password are required.';
            $this->redirect('/central-admin/login');
        }

        if ($this->auth->loginSuperAdmin($username, $password)) {
            // Check for redirect parameter
            $redirectTo = $_GET['redirect'] ?? '/central-admin/dashboard';
            $this->redirect($redirectTo);
        } else {
            $_SESSION['login_error'] = 'Invalid username or password.';
            $this->redirect('/central-admin/login');
        }
    }

    /**
     * Logout
     */
    public function logout()
    {
        $this->auth->logout();
        $this->redirect('/central-admin/login');
    }

    /**
     * Render view
     * 
     * @param string $view
     * @param array $data
     */
    private function render(string $view, array $data = [])
    {
        extract($data);
        
        ob_start();
        include __DIR__ . "/../views/{$view}.php";
        $content = ob_get_clean();
        
        include __DIR__ . '/../views/auth-layout.php';
    }

    /**
     * Redirect to URL
     * 
     * @param string $url
     */
    private function redirect(string $url)
    {
        header("Location: {$url}");
        exit;
    }
}