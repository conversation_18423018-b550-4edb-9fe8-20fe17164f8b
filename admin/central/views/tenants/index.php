<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
  <div>
    <h4 class="mb-3 mb-md-0">Tenant <PERSON></h4>
    <p class="text-muted">Tüm tenant'lar<PERSON><PERSON><PERSON><PERSON>r, d<PERSON><PERSON>leyebilir ve yönetebilirsiniz.</p>
  </div>
  <div class="d-flex align-items-center flex-wrap text-nowrap">
    <a href="/central/tenants/create" class="btn btn-primary btn-icon-text mb-2 mb-md-0">
      <i class="btn-icon-prepend" data-feather="plus"></i>
      Yeni Tenant Oluştur
    </a>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title">Tenant Listesi</h6>
        <div class="table-responsive">
          <table id="dataTableExample" class="table">
            <thead>
              <tr>
                <th class="no-sort">Domain</th>
                <th class="no-sort">Tema</th>
                <th class="no-sort">Durum</th>
                <th>Son Kullanma</th>
                <th class="no-sort">Oluşturulma</th>
                <th class="no-sort">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              <?php if (!empty($tenants)): ?>
                <?php foreach ($tenants as $tenant): ?>
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="me-2">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center">
                          <i data-feather="globe" class="icon-sm text-white"></i>
                        </div>
                      </div>
                      <div>
                        <h6 class="mb-0">
                          <a href="http://<?= htmlspecialchars($tenant['domain']) ?>" target="_blank" class="text-primary">
                            <?= htmlspecialchars($tenant['domain']) ?>
                          </a>
                        </h6>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-primary"><?= htmlspecialchars($tenant['theme_name'] ?? $tenant['theme'] ?? 'N/A') ?></span>
                  </td>
                  <td>
                    <?php if ($tenant['status'] === 'active'): ?>
                      <span class="badge bg-success">Aktif</span>
                    <?php elseif ($tenant['status'] === 'inactive'): ?>
                      <span class="badge bg-danger">Pasif</span>
                    <?php else: ?>
                      <span class="badge bg-warning">Beklemede</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if ($tenant['expired_at']): ?>
                      <?= date('d/m/Y', strtotime($tenant['expired_at'])) ?>
                    <?php else: ?>
                      <span class="badge bg-success">Sınırsız</span>
                    <?php endif; ?>
                  </td>
                  <td><?= date('d/m/Y', strtotime($tenant['created_at'])) ?></td>
                  <td>
                    <a href="/central/tenants/<?= $tenant['id'] ?>/edit" class="me-1" title="Düzenle">
                      <i class="fas fa-edit" style="font-size: 12px; color: #6c757d;"></i>
                    </a>
                    <button type="button" onclick="confirmDelete(<?= $tenant['id'] ?>, '<?= htmlspecialchars($tenant['domain']) ?>')" title="Sil" style="background: none; border: none; padding: 0;">
                      <i class="fas fa-trash" style="font-size: 12px; color: #6c757d;"></i>
                    </button>
                  </td>
                </tr>
                <?php endforeach; ?>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteModalLabel">Tenant Silme Onayı</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p><strong id="deleteTenantName"></strong> adlı tenant'ı silmek istediğinizden emin misiniz?</p>
        <p class="text-muted">Bu işlem geri alınamaz ve tenant'a ait tüm veriler silinecektir.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
        <form id="deleteForm" method="POST" style="display: inline;">
          <?php $auth = new Auth(); ?>
          <input type="hidden" name="_token" value="<?= $auth->generateCsrfToken() ?>">
          <button type="submit" class="btn btn-danger">Evet, Sil</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
function confirmDelete(tenantId, tenantName) {
  document.getElementById('deleteTenantName').textContent = tenantName;
  document.getElementById('deleteForm').action = '/central/tenants/' + tenantId + '/delete';
  
  const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
  deleteModal.show();
}
</script>