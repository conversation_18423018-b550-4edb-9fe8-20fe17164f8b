<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
  <div>
    <h4 class="mb-3 mb-md-0">Tenant <PERSON></h4>
    <p class="text-muted"><?= htmlspecialchars($tenant['domain'] ?? 'Bilinmeyen') ?> tenant'ını düzenleyin.</p>
  </div>
  <div class="d-flex align-items-center flex-wrap text-nowrap">
    <a href="/central/tenants" class="btn btn-primary btn-icon-text mb-2 mb-md-0">
      <i class="btn-icon-prepend" data-feather="arrow-left"></i>
      Geri <PERSON>ö<PERSON>
    </a>
  </div>
</div>

<?php if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <h6 class="alert-heading">Hata!</h6>
  <ul class="mb-0">
    <?php foreach ($_SESSION['errors'] as $error): ?>
      <li><?= htmlspecialchars($error) ?></li>
    <?php endforeach; ?>
  </ul>
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php unset($_SESSION['errors']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
  <?= htmlspecialchars($_SESSION['error']) ?>
  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php unset($_SESSION['error']); ?>
<?php endif; ?>

<div class="row">
  <div class="col-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h6 class="card-title">Tenant Bilgileri</h6>
        <form method="POST" action="/central/tenants/<?= $tenant['id'] ?>">
          <?php $auth = new Auth(); ?>
          <input type="hidden" name="_token" value="<?= $auth->generateCsrfToken() ?>">
          <div class="row">
            <div class="col-sm-12">
              <div class="mb-3">
                <label for="domain" class="form-label">Domain *</label>
                <input type="text" class="form-control" id="domain" name="domain" placeholder="Örn: lawyer1.test" value="<?= htmlspecialchars($_POST['domain'] ?? $tenant['domain'] ?? '') ?>" required>
                <small class="form-text text-muted">Tenant'ın erişim domain'i</small>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-sm-6">
              <div class="mb-3">
                <label for="theme" class="form-label">Tema *</label>
                <select class="form-select" id="theme" name="theme" required>
                  <option value="">Tema Seçin</option>
                  <?php 
                  $tenantCreator = new TenantCreator();
                  $availableThemes = $tenantCreator->getAvailableThemes();
                  $currentTheme = $_POST['theme'] ?? $tenant['theme'] ?? $tenant['theme_name'] ?? '';
                  foreach ($availableThemes as $theme): 
                    $selected = $currentTheme === $theme['name'] ? 'selected' : '';
                  ?>
                    <option value="<?= htmlspecialchars($theme['name']) ?>" <?= $selected ?>><?= htmlspecialchars($theme['display_name']) ?></option>
                  <?php endforeach; ?>
                </select>
                <small class="form-text text-muted">Tenant için kullanılacak tema</small>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="mb-3">
                <label for="status" class="form-label">Durum</label>
                <select class="form-select" id="status" name="status">
                  <option value="active" <?= ($_POST['status'] ?? $tenant['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Aktif</option>
                  <option value="inactive" <?= ($_POST['status'] ?? $tenant['status'] ?? 'active') === 'inactive' ? 'selected' : '' ?>>Pasif</option>
                  <option value="pending" <?= ($_POST['status'] ?? $tenant['status'] ?? 'active') === 'pending' ? 'selected' : '' ?>>Beklemede</option>
                </select>
                <small class="form-text text-muted">Tenant'ın durumu</small>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-sm-12">
              <div class="mb-3">
                <label for="expired_at" class="form-label">Son Kullanma Tarihi</label>
                <input type="date" class="form-control" id="expired_at" name="expired_at" value="<?= htmlspecialchars($_POST['expired_at'] ?? ($tenant['expired_at'] ? date('Y-m-d', strtotime($tenant['expired_at'])) : '')) ?>">
                <small class="form-text text-muted">Boş bırakılırsa sınırsız olur</small>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-12">
              <button type="submit" class="btn btn-primary">
                <i data-feather="save" class="icon-sm me-1"></i>
                Değişiklikleri Kaydet
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>