<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
  <div>
    <h4 class="mb-3 mb-md-0">Central Admin Dashboard</h4>
  </div>
</div>

<div class="row">
  <div class="col-12 col-xl-12 stretch-card">
    <div class="row flex-grow-1">
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">Toplam Tenant</h6>
              <div class="dropdown mb-2">
                <a type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="icon-lg text-secondary pb-3px" data-lucide="more-horizontal"></i>
                </a>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                  <a class="dropdown-item d-flex align-items-center" href="/central/tenants"><i data-lucide="eye" class="icon-sm me-2"></i> <span class="">Görüntüle</span></a>
                  <a class="dropdown-item d-flex align-items-center" href="/central/tenants/create"><i data-lucide="plus" class="icon-sm me-2"></i> <span class="">Yeni Ekle</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2"><?= count($tenants ?? []) ?></h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-success">
                    <span>Aktif</span>
                    <i data-lucide="trending-up" class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <i data-lucide="users" class="icon-xl text-primary"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">Aktif Tenant</h6>
              <div class="dropdown mb-2">
                <a type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="icon-lg text-secondary pb-3px" data-lucide="more-horizontal"></i>
                </a>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                  <a class="dropdown-item d-flex align-items-center" href="/central/tenants"><i data-lucide="eye" class="icon-sm me-2"></i> <span class="">Görüntüle</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2"><?= count(array_filter($tenants ?? [], fn($t) => $t['status'] === 'active')) ?></h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-success">
                    <span>Çalışıyor</span>
                    <i data-lucide="check-circle" class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <i data-lucide="check-circle" class="icon-xl text-success"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-baseline">
              <h6 class="card-title mb-0">Sistem Durumu</h6>
              <div class="dropdown mb-2">
                <a type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="icon-lg text-secondary pb-3px" data-lucide="more-horizontal"></i>
                </a>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                  <a class="dropdown-item d-flex align-items-center" href="javascript:;"><i data-lucide="activity" class="icon-sm me-2"></i> <span class="">Sistem Bilgisi</span></a>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6 col-md-12 col-xl-5">
                <h3 class="mb-2">Sağlıklı</h3>
                <div class="d-flex align-items-baseline">
                  <p class="text-success">
                    <span>Online</span>
                    <i data-lucide="wifi" class="icon-sm mb-1"></i>
                  </p>
                </div>
              </div>
              <div class="col-6 col-md-12 col-xl-7">
                <div class="mt-md-3 mt-xl-0">
                  <i data-lucide="server" class="icon-xl text-info"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-7 col-xl-8 stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Son Tenant'lar</h6>
          <div class="dropdown mb-2">
            <a type="button" id="dropdownMenuButton4" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="icon-lg text-secondary pb-3px" data-lucide="more-horizontal"></i>
            </a>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton4">
              <a class="dropdown-item d-flex align-items-center" href="/central/tenants"><i data-lucide="eye" class="icon-sm me-2"></i> <span class="">Tümünü Görüntüle</span></a>
              <a class="dropdown-item d-flex align-items-center" href="/central/tenants/create"><i data-lucide="plus" class="icon-sm me-2"></i> <span class="">Yeni Ekle</span></a>
            </div>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead>
              <tr>
                <th class="pt-0">Domain</th>
                <th class="pt-0">Tema</th>
                <th class="pt-0">Durum</th>
                <th class="pt-0">Oluşturulma</th>
                <th class="pt-0">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              <?php if (empty($tenants)): ?>
              <tr>
                <td colspan="5" class="text-center py-4">
                  <div class="text-muted">
                    <i data-feather="inbox" class="icon-lg mb-2"></i>
                    <p>Henüz tenant bulunmuyor.</p>
                    <a href="/central/tenants/create" class="btn btn-primary btn-sm">
                      <i data-feather="plus" class="icon-sm me-1"></i>
                      İlk Tenant'ı Oluştur
                    </a>
                  </div>
                </td>
              </tr>
              <?php else: ?>
                <?php 
                $recentTenants = array_slice($tenants, -5);
                foreach ($recentTenants as $tenant): ?>
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="me-2">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center">
                          <i data-feather="globe" class="icon-sm text-white"></i>
                        </div>
                      </div>
                      <div>
                        <h6 class="mb-0"><?= htmlspecialchars($tenant['domain']) ?></h6>
                      </div>
                    </div>
                  </td>
                  <td><span class="badge bg-primary"><?= htmlspecialchars($tenant['theme_name'] ?? 'N/A') ?></span></td>
                  <td>
                    <?php if ($tenant['status'] === 'active'): ?>
                      <span class="badge bg-success">Aktif</span>
                    <?php elseif ($tenant['status'] === 'inactive'): ?>
                      <span class="badge bg-danger">Pasif</span>
                    <?php else: ?>
                      <span class="badge bg-warning">Beklemede</span>
                    <?php endif; ?>
                  </td>
                  <td><?= date('d.m.Y', strtotime($tenant['created_at'])) ?></td>
                  <td>
                    <a href="/central/tenants/<?= $tenant['id'] ?>/edit" class="btn btn-sm btn-outline-primary">
                      <i data-feather="edit-2" class="icon-sm"></i>
                    </a>
                  </td>
                </tr>
                <?php endforeach; ?>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div> 
    </div>
  </div>
  <div class="col-lg-5 col-xl-4 stretch-card">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-baseline mb-2">
          <h6 class="card-title mb-0">Hızlı İşlemler</h6>
        </div>
        <div class="d-grid gap-2">
          <a href="/central/tenants/create" class="btn btn-primary">
            <i data-feather="plus" class="icon-sm me-2"></i>
            Yeni Tenant Oluştur
          </a>
          <a href="/central/tenants" class="btn btn-outline-primary">
            <i data-feather="list" class="icon-sm me-2"></i>
            Tüm Tenant'ları Görüntüle
          </a>
          <a href="javascript:;" class="btn btn-outline-secondary">
            <i data-feather="settings" class="icon-sm me-2"></i>
            Sistem Ayarları
          </a>
          <a href="javascript:;" class="btn btn-outline-info">
            <i data-feather="help-circle" class="icon-sm me-2"></i>
            Yardım & Dokümantasyon
          </a>
        </div>
      </div>
    </div>
  </div>
</div>