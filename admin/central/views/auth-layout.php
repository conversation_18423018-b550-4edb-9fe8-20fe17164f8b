<!DOCTYPE html>
<html lang="tr">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="description" content="Multi-Tenant SaaS Admin Panel">
	<meta name="author" content="Multi-Tenant System">

	<title><?php echo $title ?? 'Login'; ?> - Multi-Tenant SaaS</title>

	<!-- color-modes:js -->
	<script src="/assets/js/color-modes.js"></script>
	<!-- endinject -->

	<!-- Fonts -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
	<!-- End fonts -->

	<!-- core:css -->
	<link rel="stylesheet" href="/assets/vendors/core/core.css">
	<!-- endinject -->

	<!-- Plugin css for this page -->
	<!-- End plugin css for this page -->

	<!-- inject:css -->
	<!-- endinject -->

	<!-- Layout styles -->  
	<link rel="stylesheet" href="/assets/css/demo1/style.css">
	<!-- End layout styles -->

	<link rel="shortcut icon" href="/assets/images/favicon.png" />
</head>
<body>
	<div class="main-wrapper">
		<div class="page-wrapper full-page">
			<div class="page-content container-xxl d-flex align-items-center justify-content-center">

				<div class="row w-100 mx-0 auth-page">
					<div class="col-md-10 col-lg-8 col-xl-6 mx-auto">
						<div class="card">
							<div class="row">
								<div class="col-md-4 pe-md-0">
									<div class="auth-side-wrapper">

									</div>
								</div>
								<div class="col-md-8 ps-md-0">
									<div class="auth-form-wrapper px-4 py-5">
										<a href="#" class="nobleui-logo d-block mb-2">Multi<span>Tenant</span></a>
										<h5 class="text-secondary fw-normal mb-4">
											<?php if (defined('IS_CENTRAL_ADMIN') && IS_CENTRAL_ADMIN): ?>
												Central Admin Panel'e hoş geldiniz.
											<?php else: ?>
												Admin Panel'e hoş geldiniz.
											<?php endif; ?>
										</h5>
										
										<?php if (isset($error)): ?>
											<div class="alert alert-danger" role="alert">
												<i class="fas fa-exclamation-triangle me-2"></i>
												<?php echo htmlspecialchars($error); ?>
											</div>
										<?php endif; ?>

										<?php echo $content; ?>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>

	<!-- core:js -->
	<script src="/assets/vendors/core/core.js"></script>
	<!-- endinject -->

	<!-- Plugin js for this page -->
	<!-- End plugin js for this page -->

	<!-- inject:js -->
	<script src="/assets/js/app.js"></script>
	<!-- endinject -->

	<!-- Custom js for this page -->
	<!-- End custom js for this page -->

</body>
</html>