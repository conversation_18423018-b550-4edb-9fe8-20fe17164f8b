<!DOCTYPE html>
<html lang="tr">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="description" content="Multi-Tenant SaaS Admin Panel">
	<meta name="author" content="Multi-Tenant System">

	<title><?php echo $title ?? 'Admin Panel'; ?> - <?php echo $_ENV['APP_NAME'] ?? 'Multi-Tenant SaaS'; ?></title>

	<!-- color-modes:js -->
	<script src="/assets/js/color-modes.js"></script>
	<!-- endinject -->

	<!-- Fonts -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
	<!-- End fonts -->

	<!-- core:css -->
	<link rel="stylesheet" href="/assets/vendors/core/core.css">
	<!-- endinject -->

	<!-- Plugin css for this page -->
	<link rel="stylesheet" href="/assets/vendors/flatpickr/flatpickr.min.css">
	<link rel="stylesheet" href="/assets/vendors/datatables.net-bs5/dataTables.bootstrap5.css">
	<!-- End plugin css for this page -->

	<!-- inject:css -->
	<!-- endinject -->

	<!-- Layout styles -->  
	<link rel="stylesheet" href="/assets/css/demo1/style.css">
	<!-- End layout styles -->

	<link rel="shortcut icon" href="/assets/images/favicon.png" />
</head>
<body>
	<div class="main-wrapper">

		<!-- partial:partials/_sidebar.html -->
		<?php include __DIR__ . '/partials/sidebar.php'; ?>
		<!-- partial -->
	
		<div class="page-wrapper">
					
			<!-- partial:partials/_navbar.html -->
			<?php include __DIR__ . '/partials/navbar.php'; ?>
			<!-- partial -->

			<div class="page-content container-xxl">
				<?php if (isset($success)): ?>
					<div class="alert alert-success alert-dismissible fade show" role="alert">
						<?php echo htmlspecialchars($success); ?>
						<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
					</div>
				<?php endif; ?>

				<?php if (isset($error)): ?>
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						<?php echo htmlspecialchars($error); ?>
						<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
					</div>
				<?php endif; ?>

				<?php echo $content; ?>

			</div>

			<!-- partial:partials/_footer.html -->
			<?php include __DIR__ . '/partials/footer.php'; ?>
			<!-- partial -->
		
		</div>
	</div>

	<!-- core:js -->
	<script src="/assets/vendors/core/core.js"></script>
	<!-- endinject -->

	<!-- Plugin js for this page -->
	<script src="/assets/vendors/flatpickr/flatpickr.min.js"></script>
	<script src="/assets/vendors/apexcharts/apexcharts.min.js"></script>
	<script src="/assets/vendors/jquery/jquery.min.js"></script>
	<script src="/assets/vendors/datatables.net/dataTables.js"></script>
	<script src="/assets/vendors/datatables.net-bs5/dataTables.bootstrap5.js"></script>
	<!-- End plugin js for this page -->

	<!-- inject:js -->
	<script src="/assets/js/app.js"></script>
	<!-- endinject -->

	<!-- Custom js for this page -->
	<script src="/assets/js/dashboard.js"></script>
	<script src="/assets/js/data-table.js"></script>
	<!-- End custom js for this page -->

	<script>
		// Auto-hide alerts after 5 seconds
		setTimeout(function() {
			const alerts = document.querySelectorAll('.alert');
			alerts.forEach(alert => {
				const bsAlert = new bootstrap.Alert(alert);
				bsAlert.close();
			});
		}, 5000);
	</script>

</body>
</html>