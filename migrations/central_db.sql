-- Central database schema for multi-tenant system
-- This contains tenant management and super admin tables

-- Super admin users table
CREATE TABLE IF NOT EXISTS super_admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Tenants table
CREATE TABLE IF NOT EXISTS tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(255) UNIQUE NOT NULL,
    theme_id INT NOT NULL,
    status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'active',
    expired_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Themes table (optional - using hardcoded themes for now)
CREATE TABLE IF NOT EXISTS themes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0.0',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default themes
INSERT IGNORE INTO themes (id, name, display_name, description) VALUES
(1, 'lawyer1', 'Avukat Teması', 'Çoklu avukat için hukuk bürosu teması'),
(2, 'dietitian1', 'Diyetisyen Teması', 'Beslenme uzmanları için tema'),
(3, 'technical1', 'Teknik Servis Teması', 'Teknik servis işletmeleri için tema');

-- Insert default super admin user (password: admin123)
INSERT IGNORE INTO super_admin_users (username, email, password_hash) VALUES
('admin', '<EMAIL>', '0192023a7bbd73250516f069df18b500');

-- Create indexes for better performance
CREATE INDEX idx_tenants_domain ON tenants(domain);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_theme ON tenants(theme_id);