// Demo Styles 

.nobleui-logo {
  font-weight: 700;
  font-size: 25px;
  color: darken($primary, 50%);
  span {
    color: $primary;
    font-weight: 300;
  }
  &:hover {
    color: darken($primary, 50%);
  }
}

@if $enable-dark-mode {
  @include color-mode(dark) {
    // Dark mode only code here..
    .nobleui-logo {
      color: $light;
    }
  }
}

.main-content {
  color: var(--#{$prefix}body-color);
  font-size: 16px;
  > .page-title {
    margin-bottom: 1rem;
    font-weight: 400;
  }
  > h4 {
    margin-top: 1.5rem;
    margin-bottom: .875rem;
    &::before {
      display: block;
      height: 5.4rem;
      margin-top: -6rem;
      content: "";
    }
  }
  > hr {
    margin-top: 40px;
    margin-bottom: 40px;
  }
  .example {
    font-size: 0.875rem;
    letter-spacing: normal;
    padding: 10px;
    background-color: $card-bg;
    border: 4px solid var(--#{$prefix}border-color);
    position: relative;
    @media(min-width: 576px) {
      padding: 25px;
    }
  }
  .highlight {
    position: relative;
    /*rtl:ignore*/
    direction: ltr;
    pre {
      border: 4px solid var(--#{$prefix}border-color);
      border-top: 0;
      padding: 16px;
      margin: 0;
      background: #0d1117;
      code {
        padding: 0;
      }
    }
    .btn-clipboard {
      position: absolute;
      top: 8px;
      /*rtl:ignore*/
      right: 12px;
      font-size: 12px;
      padding: 1px 6px;
      background: rgba($secondary, .4);
      color: #fff;
      border: 0;
      transition: background .3s ease-in-out;
      &:hover {
        background: rgba($secondary, .2);
        transition: background .1s ease;
      }
    }
  }

  // RTL fix  
  > p,
  > h1,
  > h2,
  > h3,
  > h4,
  > h5,
  > h6 {
    /*rtl:raw:
      direction: ltr;
    */
  }
}

.example {
  .btn-toolbar {
    + .btn-toolbar {
      margin-top: .5rem;
    }
  }
  .btn-group {
    @extend .mb-1;
    @extend .mb-md-0;
  }
  .modal {
    &.static {
      position: static;
      display: block;
    }
  }
  .navbar {
    position: relative;
    padding: .5rem 1rem;
    left: auto;
    width: 100%;
    height: auto;
    z-index: 9;
    border-bottom: 0;
    box-shadow: none;
    .navbar-brand {
      font-size: 1.25rem;
    }
  }
  .progress {
    + .progress {
      margin-top: 10px;
    }
  }
  .perfect-scrollbar-example {
    position: relative;
    max-height: 250px;
    background: var(--#{$prefix}body-bg);
  }
  .scrollspy-example {
    position: relative;
    height: 200px;
    margin-top: .5rem;
    overflow: auto;
  }
  .scrollspy-example-2 {
    position: relative;
    height: 350px;
    overflow: auto;
  }
  nav {
    .breadcrumb {
      margin-bottom: .75rem;
    }

    &:last-child {
      .breadcrumb {
        margin-bottom: 0;
      }
    }
  }
}

.buy-now-wrapper {
  position: fixed;
  bottom: 30px;
  right: 35px;
  z-index: 998;
}