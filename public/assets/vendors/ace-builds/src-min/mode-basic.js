define("ace/mode/basic_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text_highlight_rules").TextHighlightRules,s=function(){var e=this.createKeywordMapper({"keyword.control":"FOR|TO|NEXT|GOSUB|RETURN|IF|THEN|ELSE|GOTO|ON|WHILE|WEND|TRON|TROFF","entity.name":"Auto|Call|Chain|Clear|Close|Common|Cont|Data|MERGE|ALL|Delete|DIM|EDIT|END|ERASE|ERROR|FIELD|GET|INPUT|KILL|LET|LIST|LLIST|LOAD|LSET|RSET|MERGE|NEW|NULL|OPEN|OUT|POKE|PRINT|PUT|RANDOMIZE|READ|RENUM|RESTORE|RESUME|RUN|SAVE|STOP|SWAP|WAIT|WIDTH","keyword.operator":"Mod|And|Not|Or|Xor|Eqv|Imp","support.function":"ABS|ASC|ATN|CDBL|CINT|COS|CSNG|CVI|CVS|CVD|EOF|EXP|FIX|FRE|INP|INSTR|INT|LEN|LOC|LOG|LPOS|PEEK|POS|RND|SGN|SIN|SPC|SQR|TAB|TAN|USR|VAL|VARPTR"},"identifier",!0);this.$rules={start:[{token:"string",regex:/"(?:\\.|[^"\\])*"/},{token:"support.function",regex:/(HEX|CHR|INPUT|LEFT|MID|MKI|MKS|MKD|OCT|RIGHT|SPACE|STR|STRING)\$/},{token:"entity.name",regex:/(?:DEF\s(?:SEG|USR|FN[a-zA-Z]+)|LINE\sINPUT|L?PRINT#?(?:\sUSING)?|MID\$|ON\sERROR\sGOTO|OPTION\sBASE|WRITE#?|DATE\$|INKEY\$|TIME\$)/},{token:"variable",regex:/[a-zA-Z][a-zA-Z0-9_]{0,38}[$%!#]?(?=\s*=)/},{token:"keyword.operator",regex:/\\|=|\^|\*|\/|\+|\-|<|>|-/},{token:"paren.lparen",regex:/[([]/},{token:"paren.rparen",regex:/[\)\]]/},{token:"constant.numeric",regex:/[+-]?\d+(\.\d+)?([ED][+-]?\d+)?(?:[!#])?/},{token:"constant.numeric",regex:/&[HO]?[0-9A-F]+/},{token:"comment",regex:/REM\s+.*$/},{regex:"\\w+",token:e},{token:"punctiation",regex:/[,;]/}]},this.normalizeRules()};r.inherits(s,i),t.BasicHighlightRules=s}),define("ace/mode/folding/basic",["require","exports","module","ace/lib/oop","ace/mode/folding/fold_mode","ace/range","ace/token_iterator"],function(e,t,n){"use strict";var r=e("../../lib/oop"),i=e("./fold_mode").FoldMode,s=e("../../range").Range,o=e("../../token_iterator").TokenIterator,u=t.FoldMode=function(){};r.inherits(u,i),function(){this.indentKeywords={tron:1,"while":1,"for":1,troff:-1,wend:-1,next:-1},this.foldingStartMarker=/(?:\s|^)(tron|while|for)\b/i,this.foldingStopMarker=/(?:\b)(troff|next|wend)\b/i,this.getFoldWidgetRange=function(e,t,n){var r=e.getLine(n),i=this.foldingStartMarker.test(r),s=this.foldingStopMarker.test(r);if(i||s){var o=s?this.foldingStopMarker.exec(r):this.foldingStartMarker.exec(r),u=o&&o[1].toLowerCase();if(u){var a=e.getTokenAt(n,o.index+2).type;if(a==="keyword.control")return this.basicBlock(e,n,o.index+2)}}},this.getFoldWidget=function(e,t,n){var r=e.getLine(n),i=this.foldingStartMarker.test(r),s=this.foldingStopMarker.test(r);if(i&&!s){var o=this.foldingStartMarker.exec(r),u=o&&o[1].toLowerCase();if(u){var a=e.getTokenAt(n,o.index+2).type;if(a=="keyword.control")return"start"}}if(t!="markbeginend"||!s||i&&s)return"";var o=r.match(this.foldingStopMarker),u=o&&o[1].toLowerCase();return this.indentKeywords[u]&&e.getTokenAt(n,o.index+2).type==="keyword.control"?"end":""},this.basicBlock=function(e,t,n,r){var i=new o(e,t,n),u=i.getCurrentToken();if(!u||u.type!="keyword.control")return;var a=u.value.toLowerCase(),f=[a],l=this.indentKeywords[a];if(!l)return;var c=l===-1?i.getCurrentTokenColumn():e.getLine(t).length,h=t;i.step=l===-1?i.stepBackward:i.stepForward;while(u=i.step()){a=u.value.toLowerCase();if(u.type!=="keyword.control"||!this.indentKeywords[a])continue;var p=l*this.indentKeywords[a];p>0?f.unshift(a):p<=0&&f.shift();if(f.length===0)break}if(!u)return null;if(r)return i.getCurrentTokenRange();var t=i.getCurrentTokenRow();return l===-1?new s(t,e.getLine(t).length,h,c):new s(h,c,t,i.getCurrentTokenColumn())}}.call(u.prototype)}),define("ace/mode/basic",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/basic_highlight_rules","ace/mode/folding/basic"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text").Mode,s=e("./basic_highlight_rules").BasicHighlightRules,o=e("./folding/basic").FoldMode,u=function(){this.HighlightRules=s,this.foldingRules=new o,this.$behaviour=this.$defaultBehaviour,this.indentKeywords=this.foldingRules.indentKeywords};r.inherits(u,i),function(){this.lineCommentStart=["REM"],this.getMatching=function(e,t,n,r){if(t==undefined){var i=e.selection.lead;n=i.column,t=i.row}r==undefined&&(r=!0);var s=e.getTokenAt(t,n);if(s){var o=s.value.toLowerCase();if(o in this.indentKeywords)return this.foldingRules.basicBlock(e,t,n,r)}},this.$id="ace/mode/basic"}.call(u.prototype),t.Mode=u});                (function() {
                    window.require(["ace/mode/basic"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            