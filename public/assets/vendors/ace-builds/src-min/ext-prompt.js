define("ace/autocomplete/popup",["require","exports","module","ace/virtual_renderer","ace/editor","ace/range","ace/lib/event","ace/lib/lang","ace/lib/dom","ace/config","ace/lib/useragent"],function(e,t,n){"use strict";var r=e("../virtual_renderer").VirtualRenderer,i=e("../editor").Editor,s=e("../range").Range,o=e("../lib/event"),u=e("../lib/lang"),a=e("../lib/dom"),f=e("../config").nls,l=e("./../lib/useragent"),c=function(e){return"suggest-aria-id:".concat(e)},h=l.isSafari?"menu":"listbox",p=l.isSafari?"menuitem":"option",d=l.isSafari?"aria-current":"aria-selected",v=function(e){var t=new r(e);t.$maxLines=4;var n=new i(t);return n.setHighlightActiveLine(!1),n.setShowPrintMargin(!1),n.renderer.setShowGutter(!1),n.renderer.setHighlightGutterLine(!1),n.$mouseHandler.$focusTimeout=0,n.$highlightTagPending=!0,n},m=function(){function e(e){var t=a.createElement("div"),n=v(t);e&&e.appendChild(t),t.style.display="none",n.renderer.content.style.cursor="default",n.renderer.setStyle("ace_autocomplete"),n.renderer.$textLayer.element.setAttribute("role",h),n.renderer.$textLayer.element.setAttribute("aria-roledescription",f("autocomplete.popup.aria-roledescription","Autocomplete suggestions")),n.renderer.$textLayer.element.setAttribute("aria-label",f("autocomplete.popup.aria-label","Autocomplete suggestions")),n.renderer.textarea.setAttribute("aria-hidden","true"),n.setOption("displayIndentGuides",!1),n.setOption("dragDelay",150);var r=function(){};n.focus=r,n.$isFocused=!0,n.renderer.$cursorLayer.restartTimer=r,n.renderer.$cursorLayer.element.style.opacity="0",n.renderer.$maxLines=8,n.renderer.$keepTextAreaAtCursor=!1,n.setHighlightActiveLine(!1),n.session.highlight(""),n.session.$searchHighlight.clazz="ace_highlight-marker",n.on("mousedown",function(e){var t=e.getDocumentPosition();n.selection.moveToPosition(t),m.start.row=m.end.row=t.row,e.stop()});var i,l=new s(-1,0,-1,Infinity),m=new s(-1,0,-1,Infinity);m.id=n.session.addMarker(m,"ace_active-line","fullLine"),n.setSelectOnHover=function(e){e?l.id&&(n.session.removeMarker(l.id),l.id=null):l.id=n.session.addMarker(l,"ace_line-hover","fullLine")},n.setSelectOnHover(!1),n.on("mousemove",function(e){if(!i){i=e;return}if(i.x==e.x&&i.y==e.y)return;i=e,i.scrollTop=n.renderer.scrollTop,n.isMouseOver=!0;var t=i.getDocumentPosition().row;l.start.row!=t&&(l.id||n.setRow(t),y(t))}),n.renderer.on("beforeRender",function(){if(i&&l.start.row!=-1){i.$pos=null;var e=i.getDocumentPosition().row;l.id||n.setRow(e),y(e,!0)}}),n.renderer.on("afterRender",function(){var e=n.renderer.$textLayer;for(var t=e.config.firstRow,r=e.config.lastRow;t<=r;t++){var i=e.element.childNodes[t-e.config.firstRow];i.setAttribute("role",p),i.setAttribute("aria-roledescription",f("autocomplete.popup.item.aria-roledescription","item")),i.setAttribute("aria-setsize",n.data.length),i.setAttribute("aria-describedby","doc-tooltip"),i.setAttribute("aria-posinset",t+1);var s=n.getData(t);if(s){var o="".concat(s.caption||s.value).concat(s.meta?", ".concat(s.meta):"");i.setAttribute("aria-label",o)}var u=i.querySelectorAll(".ace_completion-highlight");u.forEach(function(e){e.setAttribute("role","mark")})}}),n.renderer.on("afterRender",function(){var e=n.getRow(),t=n.renderer.$textLayer,r=t.element.childNodes[e-t.config.firstRow],i=document.activeElement;r!==n.selectedNode&&n.selectedNode&&(a.removeCssClass(n.selectedNode,"ace_selected"),n.selectedNode.removeAttribute(d),n.selectedNode.removeAttribute("id")),i.removeAttribute("aria-activedescendant"),n.selectedNode=r;if(r){var s=c(e);a.addCssClass(r,"ace_selected"),r.id=s,t.element.setAttribute("aria-activedescendant",s),i.setAttribute("aria-activedescendant",s),r.setAttribute(d,"true")}});var g=function(){y(-1)},y=function(e,t){e!==l.start.row&&(l.start.row=l.end.row=e,t||n.session._emit("changeBackMarker"),n._emit("changeHoverMarker"))};n.getHoveredRow=function(){return l.start.row},o.addListener(n.container,"mouseout",function(){n.isMouseOver=!1,g()}),n.on("hide",g),n.on("changeSelection",g),n.session.doc.getLength=function(){return n.data.length},n.session.doc.getLine=function(e){var t=n.data[e];return typeof t=="string"?t:t&&t.value||""};var b=n.session.bgTokenizer;return b.$tokenizeRow=function(e){function s(e,n){e&&r.push({type:(t.className||"")+(n||""),value:e})}var t=n.data[e],r=[];if(!t)return r;typeof t=="string"&&(t={value:t});var i=t.caption||t.value||t.name,o=i.toLowerCase(),u=(n.filterText||"").toLowerCase(),a=0,f=0;for(var l=0;l<=u.length;l++)if(l!=f&&(t.matchMask&1<<l||l==u.length)){var c=u.slice(f,l);f=l;var h=o.indexOf(c,a);if(h==-1)continue;s(i.slice(a,h),""),a=h+c.length,s(i.slice(h,a),"completion-highlight")}return s(i.slice(a,i.length),""),r.push({type:"completion-spacer",value:" "}),t.meta&&r.push({type:"completion-meta",value:t.meta}),t.message&&r.push({type:"completion-message",value:t.message}),r},b.$updateOnChange=r,b.start=r,n.session.$computeWidth=function(){return this.screenWidth=0},n.isOpen=!1,n.isTopdown=!1,n.autoSelect=!0,n.filterText="",n.isMouseOver=!1,n.data=[],n.setData=function(e,t){n.filterText=t||"",n.setValue(u.stringRepeat("\n",e.length),-1),n.data=e||[],n.setRow(0)},n.getData=function(e){return n.data[e]},n.getRow=function(){return m.start.row},n.setRow=function(e){e=Math.max(this.autoSelect?0:-1,Math.min(this.data.length-1,e)),m.start.row!=e&&(n.selection.clearSelection(),m.start.row=m.end.row=e||0,n.session._emit("changeBackMarker"),n.moveCursorTo(e||0,0),n.isOpen&&n._signal("select"))},n.on("changeSelection",function(){n.isOpen&&n.setRow(n.selection.lead.row),n.renderer.scrollCursorIntoView()}),n.hide=function(){this.container.style.display="none",n.anchorPos=null,n.anchor=null,n.isOpen&&(n.isOpen=!1,this._signal("hide"))},n.tryShow=function(e,t,r,s){if(!s&&n.isOpen&&n.anchorPos&&n.anchor&&n.anchorPos.top===e.top&&n.anchorPos.left===e.left&&n.anchor===r)return!0;var o=this.container,u=this.renderer.scrollBar.width||10,a=window.innerHeight-u,f=window.innerWidth-u,l=this.renderer,c=l.$maxLines*t*1.4,h={top:0,bottom:0,left:0},p=a-e.top-3*this.$borderSize-t,d=e.top-3*this.$borderSize;r||(d<=p||p>=c?r="bottom":r="top"),r==="top"?(h.bottom=e.top-this.$borderSize,h.top=h.bottom-c):r==="bottom"&&(h.top=e.top+t+this.$borderSize,h.bottom=h.top+c);var v=h.top>=0&&h.bottom<=a;if(!s&&!v)return!1;v?l.$maxPixelHeight=null:r==="top"?l.$maxPixelHeight=d:l.$maxPixelHeight=p,r==="top"?(o.style.top="",o.style.bottom=a+u-h.bottom+"px",n.isTopdown=!1):(o.style.top=h.top+"px",o.style.bottom="",n.isTopdown=!0),o.style.display="";var m=e.left;return m+o.offsetWidth>f&&(m=f-o.offsetWidth),o.style.left=m+"px",o.style.right="",n.isOpen||(n.isOpen=!0,this._signal("show"),i=null),n.anchorPos=e,n.anchor=r,!0},n.show=function(e,t,n){this.tryShow(e,t,n?"bottom":undefined,!0)},n.goTo=function(e){var t=this.getRow(),n=this.session.getLength()-1;switch(e){case"up":t=t<=0?n:t-1;break;case"down":t=t>=n?-1:t+1;break;case"start":t=0;break;case"end":t=n}this.setRow(t)},n.getTextLeftOffset=function(){return this.$borderSize+this.renderer.$padding+this.$imageSize},n.$imageSize=0,n.$borderSize=1,n}return e}();a.importCssString('\n.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #CAD6FA;\n    z-index: 1;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #3a674e;\n}\n.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid #abbffe;\n    margin-top: -1px;\n    background: rgba(233,233,253,0.4);\n    position: absolute;\n    z-index: 2;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid rgba(109, 150, 13, 0.8);\n    background: rgba(58, 103, 78, 0.62);\n}\n.ace_completion-meta {\n    opacity: 0.5;\n    margin-left: 0.9em;\n}\n.ace_completion-message {\n    margin-left: 0.9em;\n    color: blue;\n}\n.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #2d69c7;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #93ca12;\n}\n.ace_editor.ace_autocomplete {\n    width: 300px;\n    z-index: 200000;\n    border: 1px lightgray solid;\n    position: fixed;\n    box-shadow: 2px 3px 5px rgba(0,0,0,.2);\n    line-height: 1.4;\n    background: #fefefe;\n    color: #111;\n}\n.ace_dark.ace_editor.ace_autocomplete {\n    border: 1px #484747 solid;\n    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);\n    line-height: 1.4;\n    background: #25282c;\n    color: #c1c1c1;\n}\n.ace_autocomplete .ace_text-layer  {\n    width: calc(100% - 8px);\n}\n.ace_autocomplete .ace_line {\n    display: flex;\n    align-items: center;\n}\n.ace_autocomplete .ace_line > * {\n    min-width: 0;\n    flex: 0 0 auto;\n}\n.ace_autocomplete .ace_line .ace_ {\n    flex: 0 1 auto;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n.ace_autocomplete .ace_completion-spacer {\n    flex: 1;\n}\n.ace_autocomplete.ace_loading:after  {\n    content: "";\n    position: absolute;\n    top: 0px;\n    height: 2px;\n    width: 8%;\n    background: blue;\n    z-index: 100;\n    animation: ace_progress 3s infinite linear;\n    animation-delay: 300ms;\n    transform: translateX(-100%) scaleX(1);\n}\n@keyframes ace_progress {\n    0% { transform: translateX(-100%) scaleX(1) }\n    50% { transform: translateX(625%) scaleX(2) } \n    100% { transform: translateX(1500%) scaleX(3) } \n}\n@media (prefers-reduced-motion) {\n    .ace_autocomplete.ace_loading:after {\n        transform: translateX(625%) scaleX(2);\n        animation: none;\n     }\n}\n',"autocompletion.css",!1),t.AcePopup=m,t.$singleLineEditor=v,t.getAriaId=c}),define("ace/snippets",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter","ace/lib/lang","ace/range","ace/range_list","ace/keyboard/hash_handler","ace/tokenizer","ace/clipboard","ace/editor"],function(e,t,n){"use strict";function p(e){var t=(new Date).toLocaleString("en-us",e);return t.length==1?"0"+t:t}var r=e("./lib/dom"),i=e("./lib/oop"),s=e("./lib/event_emitter").EventEmitter,o=e("./lib/lang"),u=e("./range").Range,a=e("./range_list").RangeList,f=e("./keyboard/hash_handler").HashHandler,l=e("./tokenizer").Tokenizer,c=e("./clipboard"),h={CURRENT_WORD:function(e){return e.session.getTextRange(e.session.getWordRange())},SELECTION:function(e,t,n){var r=e.session.getTextRange();return n?r.replace(/\n\r?([ \t]*\S)/g,"\n"+n+"$1"):r},CURRENT_LINE:function(e){return e.session.getLine(e.getCursorPosition().row)},PREV_LINE:function(e){return e.session.getLine(e.getCursorPosition().row-1)},LINE_INDEX:function(e){return e.getCursorPosition().row},LINE_NUMBER:function(e){return e.getCursorPosition().row+1},SOFT_TABS:function(e){return e.session.getUseSoftTabs()?"YES":"NO"},TAB_SIZE:function(e){return e.session.getTabSize()},CLIPBOARD:function(e){return c.getText&&c.getText()},FILENAME:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0]},FILENAME_BASE:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0].replace(/\.[^.]*$/,"")},DIRECTORY:function(e){return this.FILEPATH(e).replace(/[^/\\]*$/,"")},FILEPATH:function(e){return"/not implemented.txt"},WORKSPACE_NAME:function(){return"Unknown"},FULLNAME:function(){return"Unknown"},BLOCK_COMMENT_START:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.start||""},BLOCK_COMMENT_END:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.end||""},LINE_COMMENT:function(e){var t=e.session.$mode||{};return t.lineCommentStart||""},CURRENT_YEAR:p.bind(null,{year:"numeric"}),CURRENT_YEAR_SHORT:p.bind(null,{year:"2-digit"}),CURRENT_MONTH:p.bind(null,{month:"numeric"}),CURRENT_MONTH_NAME:p.bind(null,{month:"long"}),CURRENT_MONTH_NAME_SHORT:p.bind(null,{month:"short"}),CURRENT_DATE:p.bind(null,{day:"2-digit"}),CURRENT_DAY_NAME:p.bind(null,{weekday:"long"}),CURRENT_DAY_NAME_SHORT:p.bind(null,{weekday:"short"}),CURRENT_HOUR:p.bind(null,{hour:"2-digit",hour12:!1}),CURRENT_MINUTE:p.bind(null,{minute:"2-digit"}),CURRENT_SECOND:p.bind(null,{second:"2-digit"})};h.SELECTED_TEXT=h.SELECTION;var d=function(){function e(){this.snippetMap={},this.snippetNameMap={},this.variables=h}return e.prototype.getTokenizer=function(){return e.$tokenizer||this.createTokenizer()},e.prototype.createTokenizer=function(){function t(e){return e=e.substr(1),/^\d+$/.test(e)?[{tabstopId:parseInt(e,10)}]:[{text:e}]}function n(e){return"(?:[^\\\\"+e+"]|\\\\.)"}var r={regex:"/("+n("/")+"+)/",onMatch:function(e,t,n){var r=n[0];return r.fmtString=!0,r.guard=e.slice(1,-1),r.flag="",""},next:"formatString"};return e.$tokenizer=new l({start:[{regex:/\\./,onMatch:function(e,t,n){var r=e[1];return r=="}"&&n.length?e=r:"`$\\".indexOf(r)!=-1&&(e=r),[e]}},{regex:/}/,onMatch:function(e,t,n){return[n.length?n.shift():e]}},{regex:/\$(?:\d+|\w+)/,onMatch:t},{regex:/\$\{[\dA-Z_a-z]+/,onMatch:function(e,n,r){var i=t(e.substr(1));return r.unshift(i[0]),i},next:"snippetVar"},{regex:/\n/,token:"newline",merge:!1}],snippetVar:[{regex:"\\|"+n("\\|")+"*\\|",onMatch:function(e,t,n){var r=e.slice(1,-1).replace(/\\[,|\\]|,/g,function(e){return e.length==2?e[1]:"\0"}).split("\0").map(function(e){return{value:e}});return n[0].choices=r,[r[0]]},next:"start"},r,{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"start"}],formatString:[{regex:/:/,onMatch:function(e,t,n){return n.length&&n[0].expectElse?(n[0].expectElse=!1,n[0].ifEnd={elseEnd:n[0]},[n[0].ifEnd]):":"}},{regex:/\\./,onMatch:function(e,t,n){var r=e[1];return r=="}"&&n.length?e=r:"`$\\".indexOf(r)!=-1?e=r:r=="n"?e="\n":r=="t"?e="	":"ulULE".indexOf(r)!=-1&&(e={changeCase:r,local:r>"a"}),[e]}},{regex:"/\\w*}",onMatch:function(e,t,n){var r=n.shift();return r&&(r.flag=e.slice(1,-1)),this.next=r&&r.tabstopId?"start":"",[r||e]},next:"start"},{regex:/\$(?:\d+|\w+)/,onMatch:function(e,t,n){return[{text:e.slice(1)}]}},{regex:/\${\w+/,onMatch:function(e,t,n){var r={text:e.slice(2)};return n.unshift(r),[r]},next:"formatStringVar"},{regex:/\n/,token:"newline",merge:!1},{regex:/}/,onMatch:function(e,t,n){var r=n.shift();return this.next=r&&r.tabstopId?"start":"",[r||e]},next:"start"}],formatStringVar:[{regex:/:\/\w+}/,onMatch:function(e,t,n){var r=n[0];return r.formatFunction=e.slice(2,-1),[n.shift()]},next:"formatString"},r,{regex:/:[\?\-+]?/,onMatch:function(e,t,n){e[1]=="+"&&(n[0].ifEnd=n[0]),e[1]=="?"&&(n[0].expectElse=!0)},next:"formatString"},{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"formatString"}]}),e.$tokenizer},e.prototype.tokenizeTmSnippet=function(e,t){return this.getTokenizer().getLineTokens(e,t).tokens.map(function(e){return e.value||e})},e.prototype.getVariableValue=function(e,t,n){if(/^\d+$/.test(t))return(this.variables.__||{})[t]||"";if(/^[A-Z]\d+$/.test(t))return(this.variables[t[0]+"__"]||{})[t.substr(1)]||"";t=t.replace(/^TM_/,"");if(!this.variables.hasOwnProperty(t))return"";var r=this.variables[t];return typeof r=="function"&&(r=this.variables[t](e,t,n)),r==null?"":r},e.prototype.tmStrFormat=function(e,t,n){if(!t.fmt)return e;var r=t.flag||"",i=t.guard;i=new RegExp(i,r.replace(/[^gim]/g,""));var s=typeof t.fmt=="string"?this.tokenizeTmSnippet(t.fmt,"formatString"):t.fmt,o=this,u=e.replace(i,function(){var e=o.variables.__;o.variables.__=[].slice.call(arguments);var t=o.resolveVariables(s,n),r="E";for(var i=0;i<t.length;i++){var u=t[i];if(typeof u=="object"){t[i]="";if(u.changeCase&&u.local){var a=t[i+1];a&&typeof a=="string"&&(u.changeCase=="u"?t[i]=a[0].toUpperCase():t[i]=a[0].toLowerCase(),t[i+1]=a.substr(1))}else u.changeCase&&(r=u.changeCase)}else r=="U"?t[i]=u.toUpperCase():r=="L"&&(t[i]=u.toLowerCase())}return o.variables.__=e,t.join("")});return u},e.prototype.tmFormatFunction=function(e,t,n){return t.formatFunction=="upcase"?e.toUpperCase():t.formatFunction=="downcase"?e.toLowerCase():e},e.prototype.resolveVariables=function(e,t){function f(t){var n=e.indexOf(t,s+1);n!=-1&&(s=n)}var n=[],r="",i=!0;for(var s=0;s<e.length;s++){var o=e[s];if(typeof o=="string"){n.push(o),o=="\n"?(i=!0,r=""):i&&(r=/^\t*/.exec(o)[0],i=/\S/.test(o));continue}if(!o)continue;i=!1;if(o.fmtString){var u=e.indexOf(o,s+1);u==-1&&(u=e.length),o.fmt=e.slice(s+1,u),s=u}if(o.text){var a=this.getVariableValue(t,o.text,r)+"";o.fmtString&&(a=this.tmStrFormat(a,o,t)),o.formatFunction&&(a=this.tmFormatFunction(a,o,t)),a&&!o.ifEnd?(n.push(a),f(o)):!a&&o.ifEnd&&f(o.ifEnd)}else o.elseEnd?f(o.elseEnd):o.tabstopId!=null?n.push(o):o.changeCase!=null&&n.push(o)}return n},e.prototype.getDisplayTextForSnippet=function(e,t){var n=v.call(this,e,t);return n.text},e.prototype.insertSnippetForSelection=function(e,t,n){n===void 0&&(n={});var r=v.call(this,e,t,n),i=e.getSelectionRange(),s=e.session.replace(i,r.text),o=new m(e),u=e.inVirtualSelectionMode&&e.selection.index;o.addTabstops(r.tabstops,i.start,s,u)},e.prototype.insertSnippet=function(e,t,n){n===void 0&&(n={});var r=this;if(e.inVirtualSelectionMode)return r.insertSnippetForSelection(e,t,n);e.forEachSelection(function(){r.insertSnippetForSelection(e,t,n)},null,{keepOrder:!0}),e.tabstopManager&&e.tabstopManager.tabNext()},e.prototype.$getScope=function(e){var t=e.session.$mode.$id||"";t=t.split("/").pop();if(t==="html"||t==="php"){t==="php"&&!e.session.$mode.inlinePhp&&(t="html");var n=e.getCursorPosition(),r=e.session.getState(n.row);typeof r=="object"&&(r=r[0]),r.substring&&(r.substring(0,3)=="js-"?t="javascript":r.substring(0,4)=="css-"?t="css":r.substring(0,4)=="php-"&&(t="php"))}return t},e.prototype.getActiveScopes=function(e){var t=this.$getScope(e),n=[t],r=this.snippetMap;return r[t]&&r[t].includeScopes&&n.push.apply(n,r[t].includeScopes),n.push("_"),n},e.prototype.expandWithTab=function(e,t){var n=this,r=e.forEachSelection(function(){return n.expandSnippetForSelection(e,t)},null,{keepOrder:!0});return r&&e.tabstopManager&&e.tabstopManager.tabNext(),r},e.prototype.expandSnippetForSelection=function(e,t){var n=e.getCursorPosition(),r=e.session.getLine(n.row),i=r.substring(0,n.column),s=r.substr(n.column),o=this.snippetMap,u;return this.getActiveScopes(e).some(function(e){var t=o[e];return t&&(u=this.findMatchingSnippet(t,i,s)),!!u},this),u?t&&t.dryRun?!0:(e.session.doc.removeInLine(n.row,n.column-u.replaceBefore.length,n.column+u.replaceAfter.length),this.variables.M__=u.matchBefore,this.variables.T__=u.matchAfter,this.insertSnippetForSelection(e,u.content),this.variables.M__=this.variables.T__=null,!0):!1},e.prototype.findMatchingSnippet=function(e,t,n){for(var r=e.length;r--;){var i=e[r];if(i.startRe&&!i.startRe.test(t))continue;if(i.endRe&&!i.endRe.test(n))continue;if(!i.startRe&&!i.endRe)continue;return i.matchBefore=i.startRe?i.startRe.exec(t):[""],i.matchAfter=i.endRe?i.endRe.exec(n):[""],i.replaceBefore=i.triggerRe?i.triggerRe.exec(t)[0]:"",i.replaceAfter=i.endTriggerRe?i.endTriggerRe.exec(n)[0]:"",i}},e.prototype.register=function(e,t){function s(e){return e&&!/^\^?\(.*\)\$?$|^\\b$/.test(e)&&(e="(?:"+e+")"),e||""}function u(e,t,n){return e=s(e),t=s(t),n?(e=t+e,e&&e[e.length-1]!="$"&&(e+="$")):(e+=t,e&&e[0]!="^"&&(e="^"+e)),new RegExp(e)}function a(e){e.scope||(e.scope=t||"_"),t=e.scope,n[t]||(n[t]=[],r[t]={});var s=r[t];if(e.name){var a=s[e.name];a&&i.unregister(a),s[e.name]=e}n[t].push(e),e.prefix&&(e.tabTrigger=e.prefix),!e.content&&e.body&&(e.content=Array.isArray(e.body)?e.body.join("\n"):e.body),e.tabTrigger&&!e.trigger&&(!e.guard&&/^\w/.test(e.tabTrigger)&&(e.guard="\\b"),e.trigger=o.escapeRegExp(e.tabTrigger));if(!e.trigger&&!e.guard&&!e.endTrigger&&!e.endGuard)return;e.startRe=u(e.trigger,e.guard,!0),e.triggerRe=new RegExp(e.trigger),e.endRe=u(e.endTrigger,e.endGuard,!0),e.endTriggerRe=new RegExp(e.endTrigger)}var n=this.snippetMap,r=this.snippetNameMap,i=this;e||(e=[]),Array.isArray(e)?e.forEach(a):Object.keys(e).forEach(function(t){a(e[t])}),this._signal("registerSnippets",{scope:t})},e.prototype.unregister=function(e,t){function i(e){var i=r[e.scope||t];if(i&&i[e.name]){delete i[e.name];var s=n[e.scope||t],o=s&&s.indexOf(e);o>=0&&s.splice(o,1)}}var n=this.snippetMap,r=this.snippetNameMap;e.content?i(e):Array.isArray(e)&&e.forEach(i)},e.prototype.parseSnippetFile=function(e){e=e.replace(/\r/g,"");var t=[],n={},r=/^#.*|^({[\s\S]*})\s*$|^(\S+) (.*)$|^((?:\n*\t.*)+)/gm,i;while(i=r.exec(e)){if(i[1])try{n=JSON.parse(i[1]),t.push(n)}catch(s){}if(i[4])n.content=i[4].replace(/^\t/gm,""),t.push(n),n={};else{var o=i[2],u=i[3];if(o=="regex"){var a=/\/((?:[^\/\\]|\\.)*)|$/g;n.guard=a.exec(u)[1],n.trigger=a.exec(u)[1],n.endTrigger=a.exec(u)[1],n.endGuard=a.exec(u)[1]}else o=="snippet"?(n.tabTrigger=u.match(/^\S*/)[0],n.name||(n.name=u)):o&&(n[o]=u)}}return t},e.prototype.getSnippetByName=function(e,t){var n=this.snippetNameMap,r;return this.getActiveScopes(t).some(function(t){var i=n[t];return i&&(r=i[e]),!!r},this),r},e}();i.implement(d.prototype,s);var v=function(e,t,n){function l(e){var t=[];for(var n=0;n<e.length;n++){var r=e[n];if(typeof r=="object"){if(f[r.tabstopId])continue;var i=e.lastIndexOf(r,n-1);r=t[i]||{tabstopId:r.tabstopId}}t[n]=r}return t}n===void 0&&(n={});var r=e.getCursorPosition(),i=e.session.getLine(r.row),s=e.session.getTabString(),o=i.match(/^\s*/)[0];r.column<o.length&&(o=o.slice(0,r.column)),t=t.replace(/\r/g,"");var u=this.tokenizeTmSnippet(t);u=this.resolveVariables(u,e),u=u.map(function(e){return e=="\n"&&!n.excludeExtraIndent?e+o:typeof e=="string"?e.replace(/\t/g,s):e});var a=[];u.forEach(function(e,t){if(typeof e!="object")return;var n=e.tabstopId,r=a[n];r||(r=a[n]=[],r.index=n,r.value="",r.parents={});if(r.indexOf(e)!==-1)return;e.choices&&!r.choices&&(r.choices=e.choices),r.push(e);var i=u.indexOf(e,t+1);if(i===-1)return;var s=u.slice(t+1,i),o=s.some(function(e){return typeof e=="object"});o&&!r.value?r.value=s:s.length&&(!r.value||typeof r.value!="string")&&(r.value=s.join(""))}),a.forEach(function(e){e.length=0});var f={};for(var c=0;c<u.length;c++){var h=u[c];if(typeof h!="object")continue;var p=h.tabstopId,d=a[p],v=u.indexOf(h,c+1);if(f[p]){f[p]===h&&(delete f[p],Object.keys(f).forEach(function(e){d.parents[e]=!0}));continue}f[p]=h;var m=d.value;typeof m!="string"?m=l(m):h.fmt&&(m=this.tmStrFormat(m,h,e)),u.splice.apply(u,[c+1,Math.max(0,v-c)].concat(m,h)),d.indexOf(h)===-1&&d.push(h)}var g=0,y=0,b="";return u.forEach(function(e){if(typeof e=="string"){var t=e.split("\n");t.length>1?(y=t[t.length-1].length,g+=t.length-1):y+=e.length,b+=e}else e&&(e.start?e.end={row:g,column:y}:e.start={row:g,column:y})}),{text:b,tabstops:a,tokens:u}},m=function(){function e(e){this.index=0,this.ranges=[],this.tabstops=[];if(e.tabstopManager)return e.tabstopManager;e.tabstopManager=this,this.$onChange=this.onChange.bind(this),this.$onChangeSelection=o.delayedCall(this.onChangeSelection.bind(this)).schedule,this.$onChangeSession=this.onChangeSession.bind(this),this.$onAfterExec=this.onAfterExec.bind(this),this.attach(e)}return e.prototype.attach=function(e){this.$openTabstops=null,this.selectedTabstop=null,this.editor=e,this.session=e.session,this.editor.on("change",this.$onChange),this.editor.on("changeSelection",this.$onChangeSelection),this.editor.on("changeSession",this.$onChangeSession),this.editor.commands.on("afterExec",this.$onAfterExec),this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler)},e.prototype.detach=function(){this.tabstops.forEach(this.removeTabstopMarkers,this),this.ranges.length=0,this.tabstops.length=0,this.selectedTabstop=null,this.editor.off("change",this.$onChange),this.editor.off("changeSelection",this.$onChangeSelection),this.editor.off("changeSession",this.$onChangeSession),this.editor.commands.off("afterExec",this.$onAfterExec),this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.tabstopManager=null,this.session=null,this.editor=null},e.prototype.onChange=function(e){var t=e.action[0]=="r",n=this.selectedTabstop||{},r=n.parents||{},i=this.tabstops.slice();for(var s=0;s<i.length;s++){var o=i[s],u=o==n||r[o.index];o.rangeList.$bias=u?0:1;if(e.action=="remove"&&o!==n){var a=o.parents&&o.parents[n.index],f=o.rangeList.pointIndex(e.start,a);f=f<0?-f-1:f+1;var l=o.rangeList.pointIndex(e.end,a);l=l<0?-l-1:l-1;var c=o.rangeList.ranges.slice(f,l);for(var h=0;h<c.length;h++)this.removeRange(c[h])}o.rangeList.$onChange(e)}var p=this.session;!this.$inChange&&t&&p.getLength()==1&&!p.getValue()&&this.detach()},e.prototype.updateLinkedFields=function(){var e=this.selectedTabstop;if(!e||!e.hasLinkedRanges||!e.firstNonLinked)return;this.$inChange=!0;var n=this.session,r=n.getTextRange(e.firstNonLinked);for(var i=0;i<e.length;i++){var s=e[i];if(!s.linked)continue;var o=s.original,u=t.snippetManager.tmStrFormat(r,o,this.editor);n.replace(s,u)}this.$inChange=!1},e.prototype.onAfterExec=function(e){e.command&&!e.command.readOnly&&this.updateLinkedFields()},e.prototype.onChangeSelection=function(){if(!this.editor)return;var e=this.editor.selection.lead,t=this.editor.selection.anchor,n=this.editor.selection.isEmpty();for(var r=0;r<this.ranges.length;r++){if(this.ranges[r].linked)continue;var i=this.ranges[r].contains(e.row,e.column),s=n||this.ranges[r].contains(t.row,t.column);if(i&&s)return}this.detach()},e.prototype.onChangeSession=function(){this.detach()},e.prototype.tabNext=function(e){var t=this.tabstops.length,n=this.index+(e||1);n=Math.min(Math.max(n,1),t),n==t&&(n=0),this.selectTabstop(n),this.updateTabstopMarkers(),n===0&&this.detach()},e.prototype.selectTabstop=function(e){this.$openTabstops=null;var t=this.tabstops[this.index];t&&this.addTabstopMarkers(t),this.index=e,t=this.tabstops[this.index];if(!t||!t.length)return;this.selectedTabstop=t;var n=t.firstNonLinked||t;t.choices&&(n.cursor=n.start);if(!this.editor.inVirtualSelectionMode){var r=this.editor.multiSelect;r.toSingleRange(n);for(var i=0;i<t.length;i++){if(t.hasLinkedRanges&&t[i].linked)continue;r.addRange(t[i].clone(),!0)}}else this.editor.selection.fromOrientedRange(n);this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler),this.selectedTabstop&&this.selectedTabstop.choices&&this.editor.execCommand("startAutocomplete",{matches:this.selectedTabstop.choices})},e.prototype.addTabstops=function(e,t,n){var r=this.useLink||!this.editor.getOption("enableMultiselect");this.$openTabstops||(this.$openTabstops=[]);if(!e[0]){var i=u.fromPoints(n,n);y(i.start,t),y(i.end,t),e[0]=[i],e[0].index=0}var s=this.index,o=[s+1,0],f=this.ranges,l=this.snippetId=(this.snippetId||0)+1;e.forEach(function(e,n){var i=this.$openTabstops[n]||e;i.snippetId=l;for(var s=0;s<e.length;s++){var c=e[s],h=u.fromPoints(c.start,c.end||c.start);g(h.start,t),g(h.end,t),h.original=c,h.tabstop=i,f.push(h),i!=e?i.unshift(h):i[s]=h,c.fmtString||i.firstNonLinked&&r?(h.linked=!0,i.hasLinkedRanges=!0):i.firstNonLinked||(i.firstNonLinked=h)}i.firstNonLinked||(i.hasLinkedRanges=!1),i===e&&(o.push(i),this.$openTabstops[n]=i),this.addTabstopMarkers(i),i.rangeList=i.rangeList||new a,i.rangeList.$bias=0,i.rangeList.addList(i)},this),o.length>2&&(this.tabstops.length&&o.push(o.splice(2,1)[0]),this.tabstops.splice.apply(this.tabstops,o))},e.prototype.addTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){e.markerId||(e.markerId=t.addMarker(e,"ace_snippet-marker","text"))})},e.prototype.removeTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){t.removeMarker(e.markerId),e.markerId=null})},e.prototype.updateTabstopMarkers=function(){if(!this.selectedTabstop)return;var e=this.selectedTabstop.snippetId;this.selectedTabstop.index===0&&e--,this.tabstops.forEach(function(t){t.snippetId===e?this.addTabstopMarkers(t):this.removeTabstopMarkers(t)},this)},e.prototype.removeRange=function(e){var t=e.tabstop.indexOf(e);t!=-1&&e.tabstop.splice(t,1),t=this.ranges.indexOf(e),t!=-1&&this.ranges.splice(t,1),t=e.tabstop.rangeList.ranges.indexOf(e),t!=-1&&e.tabstop.splice(t,1),this.session.removeMarker(e.markerId),e.tabstop.length||(t=this.tabstops.indexOf(e.tabstop),t!=-1&&this.tabstops.splice(t,1),this.tabstops.length||this.detach())},e}();m.prototype.keyboardHandler=new f,m.prototype.keyboardHandler.bindKeys({Tab:function(e){if(t.snippetManager&&t.snippetManager.expandWithTab(e))return;e.tabstopManager.tabNext(1),e.renderer.scrollCursorIntoView()},"Shift-Tab":function(e){e.tabstopManager.tabNext(-1),e.renderer.scrollCursorIntoView()},Esc:function(e){e.tabstopManager.detach()}});var g=function(e,t){e.row==0&&(e.column+=t.column),e.row+=t.row},y=function(e,t){e.row==t.row&&(e.column-=t.column),e.row-=t.row};r.importCssString("\n.ace_snippet-marker {\n    -moz-box-sizing: border-box;\n    box-sizing: border-box;\n    background: rgba(194, 193, 208, 0.09);\n    border: 1px dotted rgba(211, 208, 235, 0.62);\n    position: absolute;\n}","snippets.css",!1),t.snippetManager=new d;var b=e("./editor").Editor;(function(){this.insertSnippet=function(e,n){return t.snippetManager.insertSnippet(this,e,n)},this.expandSnippet=function(e){return t.snippetManager.expandWithTab(this,e)}}).call(b.prototype)}),define("ace/autocomplete/inline_screenreader",["require","exports","module"],function(e,t,n){"use strict";var r=function(){function e(e){this.editor=e,this.screenReaderDiv=document.createElement("div"),this.screenReaderDiv.classList.add("ace_screenreader-only"),this.editor.container.appendChild(this.screenReaderDiv)}return e.prototype.setScreenReaderContent=function(e){!this.popup&&this.editor.completer&&this.editor.completer.popup&&(this.popup=this.editor.completer.popup,this.popup.renderer.on("afterRender",function(){var e=this.popup.getRow(),t=this.popup.renderer.$textLayer,n=t.element.childNodes[e-t.config.firstRow];if(n){var r="doc-tooltip ";for(var i=0;i<this._lines.length;i++)r+="ace-inline-screenreader-line-".concat(i," ");n.setAttribute("aria-describedby",r)}}.bind(this)));while(this.screenReaderDiv.firstChild)this.screenReaderDiv.removeChild(this.screenReaderDiv.firstChild);this._lines=e.split(/\r\n|\r|\n/);var t=this.createCodeBlock();this.screenReaderDiv.appendChild(t)},e.prototype.destroy=function(){this.screenReaderDiv.remove()},e.prototype.createCodeBlock=function(){var e=document.createElement("pre");e.setAttribute("id","ace-inline-screenreader");for(var t=0;t<this._lines.length;t++){var n=document.createElement("code");n.setAttribute("id","ace-inline-screenreader-line-".concat(t));var r=document.createTextNode(this._lines[t]);n.appendChild(r),e.appendChild(n)}return e},e}();t.AceInlineScreenReader=r}),define("ace/autocomplete/inline",["require","exports","module","ace/snippets","ace/autocomplete/inline_screenreader"],function(e,t,n){"use strict";var r=e("../snippets").snippetManager,i=e("./inline_screenreader").AceInlineScreenReader,s=function(){function e(){this.editor=null}return e.prototype.show=function(e,t,n){n=n||"",e&&this.editor&&this.editor!==e&&(this.hide(),this.editor=null,this.inlineScreenReader=null);if(!e||!t)return!1;this.inlineScreenReader||(this.inlineScreenReader=new i(e));var s=t.snippet?r.getDisplayTextForSnippet(e,t.snippet):t.value;return t.hideInlinePreview||!s||!s.startsWith(n)?!1:(this.editor=e,this.inlineScreenReader.setScreenReaderContent(s),s=s.slice(n.length),s===""?e.removeGhostText():e.setGhostText(s),!0)},e.prototype.isOpen=function(){return this.editor?!!this.editor.renderer.$ghostText:!1},e.prototype.hide=function(){return this.editor?(this.editor.removeGhostText(),!0):!1},e.prototype.destroy=function(){this.hide(),this.editor=null,this.inlineScreenReader&&(this.inlineScreenReader.destroy(),this.inlineScreenReader=null)},e}();t.AceInline=s}),define("ace/autocomplete/util",["require","exports","module"],function(e,t,n){"use strict";t.parForEach=function(e,t,n){var r=0,i=e.length;i===0&&n();for(var s=0;s<i;s++)t(e[s],function(e,t){r++,r===i&&n(e,t)})};var r=/[a-zA-Z_0-9\$\-\u00A2-\u2000\u2070-\uFFFF]/;t.retrievePrecedingIdentifier=function(e,t,n){n=n||r;var i=[];for(var s=t-1;s>=0;s--){if(!n.test(e[s]))break;i.push(e[s])}return i.reverse().join("")},t.retrieveFollowingIdentifier=function(e,t,n){n=n||r;var i=[];for(var s=t;s<e.length;s++){if(!n.test(e[s]))break;i.push(e[s])}return i},t.getCompletionPrefix=function(e){var t=e.getCursorPosition(),n=e.session.getLine(t.row),r;return e.completers.forEach(function(e){e.identifierRegexps&&e.identifierRegexps.forEach(function(e){!r&&e&&(r=this.retrievePrecedingIdentifier(n,t.column,e))}.bind(this))}.bind(this)),r||this.retrievePrecedingIdentifier(n,t.column)},t.triggerAutocomplete=function(e,t){var t=t==null?e.session.getPrecedingCharacter():t;return e.completers.some(function(e){if(e.triggerCharacters&&Array.isArray(e.triggerCharacters))return e.triggerCharacters.includes(t)})}}),define("ace/autocomplete",["require","exports","module","ace/keyboard/hash_handler","ace/autocomplete/popup","ace/autocomplete/inline","ace/autocomplete/popup","ace/autocomplete/util","ace/lib/lang","ace/lib/dom","ace/snippets","ace/config","ace/lib/event","ace/lib/scroll"],function(e,t,n){"use strict";var r=e("./keyboard/hash_handler").HashHandler,i=e("./autocomplete/popup").AcePopup,s=e("./autocomplete/inline").AceInline,o=e("./autocomplete/popup").getAriaId,u=e("./autocomplete/util"),a=e("./lib/lang"),f=e("./lib/dom"),l=e("./snippets").snippetManager,c=e("./config"),h=e("./lib/event"),p=e("./lib/scroll").preventParentScroll,d=function(e,t){t.completer&&t.completer.destroy()},v=function(){function e(){this.autoInsert=!1,this.autoSelect=!0,this.autoShown=!1,this.exactMatch=!1,this.inlineEnabled=!1,this.keyboardHandler=new r,this.keyboardHandler.bindKeys(this.commands),this.parentNode=null,this.setSelectOnHover=!1,this.hasSeen=new Set,this.showLoadingState=!1,this.stickySelectionDelay=500,this.blurListener=this.blurListener.bind(this),this.changeListener=this.changeListener.bind(this),this.mousedownListener=this.mousedownListener.bind(this),this.mousewheelListener=this.mousewheelListener.bind(this),this.onLayoutChange=this.onLayoutChange.bind(this),this.changeTimer=a.delayedCall(function(){this.updateCompletions(!0)}.bind(this)),this.tooltipTimer=a.delayedCall(this.updateDocTooltip.bind(this),50),this.popupTimer=a.delayedCall(this.$updatePopupPosition.bind(this),50),this.stickySelectionTimer=a.delayedCall(function(){this.stickySelection=!0}.bind(this),this.stickySelectionDelay),this.$firstOpenTimer=a.delayedCall(function(){var t=this.completionProvider&&this.completionProvider.initialPosition;if(this.autoShown||this.popup&&this.popup.isOpen||!t||this.editor.completers.length===0)return;this.completions=new g(e.completionsForLoading),this.openPopup(this.editor,t.prefix,!1),this.popup.renderer.setStyle("ace_loading",!0)}.bind(this),this.stickySelectionDelay)}return Object.defineProperty(e,"completionsForLoading",{get:function(){return[{caption:c.nls("autocomplete.loading","Loading..."),value:""}]},enumerable:!1,configurable:!0}),e.prototype.$init=function(){return this.popup=new i(this.parentNode||document.body||document.documentElement),this.popup.on("click",function(e){this.insertMatch(),e.stop()}.bind(this)),this.popup.focus=this.editor.focus.bind(this.editor),this.popup.on("show",this.$onPopupShow.bind(this)),this.popup.on("hide",this.$onHidePopup.bind(this)),this.popup.on("select",this.$onPopupChange.bind(this)),h.addListener(this.popup.container,"mouseout",this.mouseOutListener.bind(this)),this.popup.on("changeHoverMarker",this.tooltipTimer.bind(null,null)),this.popup.renderer.on("afterRender",this.$onPopupRender.bind(this)),this.popup},e.prototype.$initInline=function(){if(!this.inlineEnabled||this.inlineRenderer)return;return this.inlineRenderer=new s,this.inlineRenderer},e.prototype.getPopup=function(){return this.popup||this.$init()},e.prototype.$onHidePopup=function(){this.inlineRenderer&&this.inlineRenderer.hide(),this.hideDocTooltip(),this.stickySelectionTimer.cancel(),this.popupTimer.cancel(),this.stickySelection=!1},e.prototype.$seen=function(e){!this.hasSeen.has(e)&&e&&e.completer&&e.completer.onSeen&&typeof e.completer.onSeen=="function"&&(e.completer.onSeen(this.editor,e),this.hasSeen.add(e))},e.prototype.$onPopupChange=function(e){if(this.inlineRenderer&&this.inlineEnabled){var t=e?null:this.popup.getData(this.popup.getRow());this.$updateGhostText(t);if(this.popup.isMouseOver&&this.setSelectOnHover){this.tooltipTimer.call(null,null);return}this.popupTimer.schedule(),this.tooltipTimer.schedule()}else this.popupTimer.call(null,null),this.tooltipTimer.call(null,null)},e.prototype.$updateGhostText=function(e){var t=this.base.row,n=this.base.column,r=this.editor.getCursorPosition().column,i=this.editor.session.getLine(t).slice(n,r);this.inlineRenderer.show(this.editor,e,i)?this.$seen(e):this.inlineRenderer.hide()},e.prototype.$onPopupRender=function(){var e=this.inlineRenderer&&this.inlineEnabled;if(this.completions&&this.completions.filtered&&this.completions.filtered.length>0)for(var t=this.popup.getFirstVisibleRow();t<=this.popup.getLastVisibleRow();t++){var n=this.popup.getData(t);n&&(!e||n.hideInlinePreview)&&this.$seen(n)}},e.prototype.$onPopupShow=function(e){this.$onPopupChange(e),this.stickySelection=!1,this.stickySelectionDelay>=0&&this.stickySelectionTimer.schedule(this.stickySelectionDelay)},e.prototype.observeLayoutChanges=function(){if(this.$elements||!this.editor)return;window.addEventListener("resize",this.onLayoutChange,{passive:!0}),window.addEventListener("wheel",this.mousewheelListener);var e=this.editor.container.parentNode,t=[];while(e)t.push(e),e.addEventListener("scroll",this.onLayoutChange,{passive:!0}),e=e.parentNode;this.$elements=t},e.prototype.unObserveLayoutChanges=function(){var e=this;window.removeEventListener("resize",this.onLayoutChange,{passive:!0}),window.removeEventListener("wheel",this.mousewheelListener),this.$elements&&this.$elements.forEach(function(t){t.removeEventListener("scroll",e.onLayoutChange,{passive:!0})}),this.$elements=null},e.prototype.onLayoutChange=function(){if(!this.popup.isOpen)return this.unObserveLayoutChanges();this.$updatePopupPosition(),this.updateDocTooltip()},e.prototype.$updatePopupPosition=function(){var e=this.editor,t=e.renderer,n=t.layerConfig.lineHeight,r=t.$cursorLayer.getPixelPosition(this.base,!0);r.left-=this.popup.getTextLeftOffset();var i=e.container.getBoundingClientRect();r.top+=i.top-t.layerConfig.offset,r.left+=i.left-e.renderer.scrollLeft,r.left+=t.gutterWidth;var s={top:r.top,left:r.left};t.$ghostText&&t.$ghostTextWidget&&this.base.row===t.$ghostText.position.row&&(s.top+=t.$ghostTextWidget.el.offsetHeight);var o=e.container.getBoundingClientRect().bottom-n,u=o<s.top?{top:o,left:s.left}:s;if(this.popup.tryShow(u,n,"bottom"))return;if(this.popup.tryShow(r,n,"top"))return;this.popup.show(r,n)},e.prototype.openPopup=function(e,t,n){this.$firstOpenTimer.cancel(),this.popup||this.$init(),this.inlineEnabled&&!this.inlineRenderer&&this.$initInline(),this.popup.autoSelect=this.autoSelect,this.popup.setSelectOnHover(this.setSelectOnHover);var r=this.popup.getRow(),i=this.popup.data[r];this.popup.setData(this.completions.filtered,this.completions.filterText),this.editor.textInput.setAriaOptions&&this.editor.textInput.setAriaOptions({activeDescendant:o(this.popup.getRow()),inline:this.inlineEnabled}),e.keyBinding.addKeyboardHandler(this.keyboardHandler);var s;this.stickySelection&&(s=this.popup.data.indexOf(i));if(!s||s===-1)s=0;this.popup.setRow(this.autoSelect?s:-1),s===r&&i!==this.completions.filtered[s]&&this.$onPopupChange();var u=this.inlineRenderer&&this.inlineEnabled;if(s===r&&u){var a=this.popup.getData(this.popup.getRow());this.$updateGhostText(a)}n||(this.popup.setTheme(e.getTheme()),this.popup.setFontSize(e.getFontSize()),this.$updatePopupPosition(),this.tooltipNode&&this.updateDocTooltip()),this.changeTimer.cancel(),this.observeLayoutChanges()},e.prototype.detach=function(){this.editor&&(this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.off("changeSelection",this.changeListener),this.editor.off("blur",this.blurListener),this.editor.off("mousedown",this.mousedownListener),this.editor.off("mousewheel",this.mousewheelListener)),this.$firstOpenTimer.cancel(),this.changeTimer.cancel(),this.hideDocTooltip(),this.completionProvider&&this.completionProvider.detach(),this.popup&&this.popup.isOpen&&this.popup.hide(),this.popup&&this.popup.renderer&&this.popup.renderer.off("afterRender",this.$onPopupRender),this.base&&this.base.detach(),this.activated=!1,this.completionProvider=this.completions=this.base=null,this.unObserveLayoutChanges()},e.prototype.changeListener=function(e){var t=this.editor.selection.lead;(t.row!=this.base.row||t.column<this.base.column)&&this.detach(),this.activated?this.changeTimer.schedule():this.detach()},e.prototype.blurListener=function(e){var t=document.activeElement,n=this.editor.textInput.getElement(),r=e.relatedTarget&&this.tooltipNode&&this.tooltipNode.contains(e.relatedTarget),i=this.popup&&this.popup.container;t!=n&&t.parentNode!=i&&!r&&t!=this.tooltipNode&&e.relatedTarget!=n&&this.detach()},e.prototype.mousedownListener=function(e){this.detach()},e.prototype.mousewheelListener=function(e){this.popup&&!this.popup.isMouseOver&&this.detach()},e.prototype.mouseOutListener=function(e){this.popup.isOpen&&this.$updatePopupPosition()},e.prototype.goTo=function(e){this.popup.goTo(e)},e.prototype.insertMatch=function(e,t){e||(e=this.popup.getData(this.popup.getRow()));if(!e)return!1;if(e.value==="")return this.detach();var n=this.completions,r=this.getCompletionProvider().insertMatch(this.editor,e,n.filterText,t);return this.completions==n&&this.detach(),r},e.prototype.showPopup=function(e,t){this.editor&&this.detach(),this.activated=!0,this.editor=e,e.completer!=this&&(e.completer&&e.completer.detach(),e.completer=this),e.on("changeSelection",this.changeListener),e.on("blur",this.blurListener),e.on("mousedown",this.mousedownListener),e.on("mousewheel",this.mousewheelListener),this.updateCompletions(!1,t)},e.prototype.getCompletionProvider=function(e){return this.completionProvider||(this.completionProvider=new m(e)),this.completionProvider},e.prototype.gatherCompletions=function(e,t){return this.getCompletionProvider().gatherCompletions(e,t)},e.prototype.updateCompletions=function(t,n){if(t&&this.base&&this.completions){var r=this.editor.getCursorPosition(),i=this.editor.session.getTextRange({start:this.base,end:r});if(i==this.completions.filterText)return;this.completions.setFilter(i);if(!this.completions.filtered.length)return this.detach();if(this.completions.filtered.length==1&&this.completions.filtered[0].value==i&&!this.completions.filtered[0].snippet)return this.detach();this.openPopup(this.editor,i,t);return}if(n&&n.matches){var r=this.editor.getSelectionRange().start;return this.base=this.editor.session.doc.createAnchor(r.row,r.column),this.base.$insertRight=!0,this.completions=new g(n.matches),this.getCompletionProvider().completions=this.completions,this.openPopup(this.editor,"",t)}var s=this.editor.getSession(),r=this.editor.getCursorPosition(),i=u.getCompletionPrefix(this.editor);this.base=s.doc.createAnchor(r.row,r.column-i.length),this.base.$insertRight=!0;var o={exactMatch:this.exactMatch,ignoreCaption:this.ignoreCaption};this.getCompletionProvider({prefix:i,pos:r}).provideCompletions(this.editor,o,function(n,r,i){var s=r.filtered,o=u.getCompletionPrefix(this.editor);this.$firstOpenTimer.cancel();if(i){if(!s.length){var a=!this.autoShown&&this.emptyMessage;typeof a=="function"&&(a=this.emptyMessage(o));if(a){var f=[{caption:a,value:""}];this.completions=new g(f),this.openPopup(this.editor,o,t),this.popup.renderer.setStyle("ace_loading",!1),this.popup.renderer.setStyle("ace_empty-message",!0);return}return this.detach()}if(s.length==1&&s[0].value==o&&!s[0].snippet)return this.detach();if(this.autoInsert&&!this.autoShown&&s.length==1)return this.insertMatch(s[0])}this.completions=!i&&this.showLoadingState?new g(e.completionsForLoading.concat(s),r.filterText):r,this.openPopup(this.editor,o,t),this.popup.renderer.setStyle("ace_empty-message",!1),this.popup.renderer.setStyle("ace_loading",!i)}.bind(this)),this.showLoadingState&&!this.autoShown&&(!this.popup||!this.popup.isOpen)&&this.$firstOpenTimer.delay(this.stickySelectionDelay/2)},e.prototype.cancelContextMenu=function(){this.editor.$mouseHandler.cancelContextMenu()},e.prototype.updateDocTooltip=function(){var e=this.popup,t=this.completions&&this.completions.filtered,n=t&&(t[e.getHoveredRow()]||t[e.getRow()]),r=null;if(!n||!this.editor||!this.popup.isOpen)return this.hideDocTooltip();var i=this.editor.completers.length;for(var s=0;s<i;s++){var o=this.editor.completers[s];if(o.getDocTooltip&&n.completerId===o.id){r=o.getDocTooltip(n);break}}!r&&typeof n!="string"&&(r=n),typeof r=="string"&&(r={docText:r});if(!r||!r.docHTML&&!r.docText)return this.hideDocTooltip();this.showDocTooltip(r)},e.prototype.showDocTooltip=function(e){this.tooltipNode||(this.tooltipNode=f.createElement("div"),this.tooltipNode.style.margin="0",this.tooltipNode.style.pointerEvents="auto",this.tooltipNode.style.overscrollBehavior="contain",this.tooltipNode.tabIndex=-1,this.tooltipNode.onblur=this.blurListener.bind(this),this.tooltipNode.onclick=this.onTooltipClick.bind(this),this.tooltipNode.id="doc-tooltip",this.tooltipNode.setAttribute("role","tooltip"),this.tooltipNode.addEventListener("wheel",p));var t=this.editor.renderer.theme;this.tooltipNode.className="ace_tooltip ace_doc-tooltip "+(t.isDark?"ace_dark ":"")+(t.cssClass||"");var n=this.tooltipNode;e.docHTML?n.innerHTML=e.docHTML:e.docText&&(n.textContent=e.docText),n.parentNode||this.popup.container.appendChild(this.tooltipNode);var r=this.popup,i=r.container.getBoundingClientRect(),s=400,o=300,u=r.renderer.scrollBar.width||10,a=i.left,l=window.innerWidth-i.right-u,c=r.isTopdown?i.top:window.innerHeight-u-i.bottom,h=[Math.min(l/s,1),Math.min(a/s,1),Math.min(c/o*.9)],d=Math.max.apply(Math,h),v=n.style;v.display="block",d==h[0]?(v.left=i.right+1+"px",v.right="",v.maxWidth=s*d+"px",v.top=i.top+"px",v.bottom="",v.maxHeight=Math.min(window.innerHeight-u-i.top,o)+"px"):d==h[1]?(v.right=window.innerWidth-i.left+"px",v.left="",v.maxWidth=s*d+"px",v.top=i.top+"px",v.bottom="",v.maxHeight=Math.min(window.innerHeight-u-i.top,o)+"px"):d==h[2]&&(v.left=window.innerWidth-i.left+"px",v.maxWidth=Math.min(s,window.innerWidth)+"px",r.isTopdown?(v.top=i.bottom+"px",v.left=i.left+"px",v.right="",v.bottom="",v.maxHeight=Math.min(window.innerHeight-u-i.bottom,o)+"px"):(v.top=r.container.offsetTop-n.offsetHeight+"px",v.left=i.left+"px",v.right="",v.bottom="",v.maxHeight=Math.min(r.container.offsetTop,o)+"px"))},e.prototype.hideDocTooltip=function(){this.tooltipTimer.cancel();if(!this.tooltipNode)return;var e=this.tooltipNode;!this.editor.isFocused()&&document.activeElement==e&&this.editor.focus(),this.tooltipNode=null,e.parentNode&&e.parentNode.removeChild(e)},e.prototype.onTooltipClick=function(e){var t=e.target;while(t&&t!=this.tooltipNode){if(t.nodeName=="A"&&t.href){t.rel="noreferrer",t.target="_blank";break}t=t.parentNode}},e.prototype.destroy=function(){this.detach();if(this.popup){this.popup.destroy();var e=this.popup.container;e&&e.parentNode&&e.parentNode.removeChild(e)}this.editor&&this.editor.completer==this&&(this.editor.off("destroy",d),this.editor.completer=null),this.inlineRenderer=this.popup=this.editor=null},e.for=function(t){return t.completer instanceof e?t.completer:(t.completer&&(t.completer.destroy(),t.completer=null),c.get("sharedPopups")?(e.$sharedInstance||(e.$sharedInstance=new e),t.completer=e.$sharedInstance):(t.completer=new e,t.once("destroy",d)),t.completer)},e}();v.prototype.commands={Up:function(e){e.completer.goTo("up")},Down:function(e){e.completer.goTo("down")},"Ctrl-Up|Ctrl-Home":function(e){e.completer.goTo("start")},"Ctrl-Down|Ctrl-End":function(e){e.completer.goTo("end")},Esc:function(e){e.completer.detach()},Return:function(e){return e.completer.insertMatch()},"Shift-Return":function(e){e.completer.insertMatch(null,{deleteSuffix:!0})},Tab:function(e){var t=e.completer.insertMatch();if(!!t||!!e.tabstopManager)return t;e.completer.goTo("down")},Backspace:function(e){e.execCommand("backspace");var t=u.getCompletionPrefix(e);!t&&e.completer&&e.completer.detach()},PageUp:function(e){e.completer.popup.gotoPageUp()},PageDown:function(e){e.completer.popup.gotoPageDown()}},v.startCommand={name:"startAutocomplete",exec:function(e,t){var n=v.for(e);n.autoInsert=!1,n.autoSelect=!0,n.autoShown=!1,n.showPopup(e,t),n.cancelContextMenu()},bindKey:"Ctrl-Space|Ctrl-Shift-Space|Alt-Space"};var m=function(){function e(e){this.initialPosition=e,this.active=!0}return e.prototype.insertByIndex=function(e,t,n){return!this.completions||!this.completions.filtered?!1:this.insertMatch(e,this.completions.filtered[t],n)},e.prototype.insertMatch=function(e,t,n){if(!t)return!1;e.startOperation({command:{name:"insertMatch"}});if(t.completer&&t.completer.insertMatch)t.completer.insertMatch(e,t);else{if(!this.completions)return!1;var r=this.completions.filterText.length,i=0;t.range&&t.range.start.row===t.range.end.row&&(r-=this.initialPosition.prefix.length,r+=this.initialPosition.pos.column-t.range.start.column,i+=t.range.end.column-this.initialPosition.pos.column);if(r||i){var s;e.selection.getAllRanges?s=e.selection.getAllRanges():s=[e.getSelectionRange()];for(var o=0,u;u=s[o];o++)u.start.column-=r,u.end.column+=i,e.session.remove(u)}t.snippet?l.insertSnippet(e,t.snippet):this.$insertString(e,t),t.completer&&t.completer.onInsert&&typeof t.completer.onInsert=="function"&&t.completer.onInsert(e,t),t.command&&t.command==="startAutocomplete"&&e.execCommand(t.command)}return e.endOperation(),!0},e.prototype.$insertString=function(e,t){var n=t.value||t;e.execCommand("insertstring",n)},e.prototype.gatherCompletions=function(e,t){var n=e.getSession(),r=e.getCursorPosition(),i=u.getCompletionPrefix(e),s=[];this.completers=e.completers;var o=e.completers.length;return e.completers.forEach(function(a,f){a.getCompletions(e,n,r,i,function(n,r){a.hideInlinePreview&&(r=r.map(function(e){return Object.assign(e,{hideInlinePreview:a.hideInlinePreview})})),!n&&r&&(s=s.concat(r)),t(null,{prefix:u.getCompletionPrefix(e),matches:s,finished:--o===0})})}),!0},e.prototype.provideCompletions=function(e,t,n){var r=function(e){var r=e.prefix,i=e.matches;this.completions=new g(i),t.exactMatch&&(this.completions.exactMatch=!0),t.ignoreCaption&&(this.completions.ignoreCaption=!0),this.completions.setFilter(r),(e.finished||this.completions.filtered.length)&&n(null,this.completions,e.finished)}.bind(this),i=!0,s=null;this.gatherCompletions(e,function(e,t){if(!this.active)return;e&&(n(e,[],!0),this.detach());var o=t.prefix;if(o.indexOf(t.prefix)!==0)return;if(i){s=t;return}r(t)}.bind(this)),i=!1;if(s){var o=s;s=null,r(o)}},e.prototype.detach=function(){this.active=!1,this.completers&&this.completers.forEach(function(e){typeof e.cancel=="function"&&e.cancel()})},e}(),g=function(){function e(e,t){this.all=e,this.filtered=e,this.filterText=t||"",this.exactMatch=!1,this.ignoreCaption=!1}return e.prototype.setFilter=function(e){if(e.length>this.filterText&&e.lastIndexOf(this.filterText,0)===0)var t=this.filtered;else var t=this.all;this.filterText=e,t=this.filterCompletions(t,this.filterText),t=t.sort(function(e,t){return t.exactMatch-e.exactMatch||t.$score-e.$score||(e.caption||e.value).localeCompare(t.caption||t.value)});var n=null;t=t.filter(function(e){var t=e.snippet||e.caption||e.value;return t===n?!1:(n=t,!0)}),this.filtered=t},e.prototype.filterCompletions=function(e,t){var n=[],r=t.toUpperCase(),i=t.toLowerCase();e:for(var s=0,o;o=e[s];s++){if(o.skipFilter){o.$score=o.score,n.push(o);continue}var u=!this.ignoreCaption&&o.caption||o.value||o.snippet;if(!u)continue;var a=-1,f=0,l=0,c,h;if(this.exactMatch){if(t!==u.substr(0,t.length))continue e}else{var p=u.toLowerCase().indexOf(i);if(p>-1)l=p;else for(var d=0;d<t.length;d++){var v=u.indexOf(i[d],a+1),m=u.indexOf(r[d],a+1);c=v>=0?m<0||v<m?v:m:m;if(c<0)continue e;h=c-a-1,h>0&&(a===-1&&(l+=10),l+=h,f|=1<<d),a=c}}o.matchMask=f,o.exactMatch=l?0:1,o.$score=(o.score||0)-l,n.push(o)}return n},e}();t.Autocomplete=v,t.CompletionProvider=m,t.FilteredList=g}),define("ace/ext/menu_tools/settings_menu.css",["require","exports","module"],function(e,t,n){n.exports="#ace_settingsmenu, #kbshortcutmenu {\n    background-color: #F7F7F7;\n    color: black;\n    box-shadow: -5px 4px 5px rgba(126, 126, 126, 0.55);\n    padding: 1em 0.5em 2em 1em;\n    overflow: auto;\n    position: absolute;\n    margin: 0;\n    bottom: 0;\n    right: 0;\n    top: 0;\n    z-index: 9991;\n    cursor: default;\n}\n\n.ace_dark #ace_settingsmenu, .ace_dark #kbshortcutmenu {\n    box-shadow: -20px 10px 25px rgba(126, 126, 126, 0.25);\n    background-color: rgba(255, 255, 255, 0.6);\n    color: black;\n}\n\n.ace_optionsMenuEntry:hover {\n    background-color: rgba(100, 100, 100, 0.1);\n    transition: all 0.3s\n}\n\n.ace_closeButton {\n    background: rgba(245, 146, 146, 0.5);\n    border: 1px solid #F48A8A;\n    border-radius: 50%;\n    padding: 7px;\n    position: absolute;\n    right: -8px;\n    top: -8px;\n    z-index: 100000;\n}\n.ace_closeButton{\n    background: rgba(245, 146, 146, 0.9);\n}\n.ace_optionsMenuKey {\n    color: darkslateblue;\n    font-weight: bold;\n}\n.ace_optionsMenuCommand {\n    color: darkcyan;\n    font-weight: normal;\n}\n.ace_optionsMenuEntry input, .ace_optionsMenuEntry button {\n    vertical-align: middle;\n}\n\n.ace_optionsMenuEntry button[ace_selected_button=true] {\n    background: #e7e7e7;\n    box-shadow: 1px 0px 2px 0px #adadad inset;\n    border-color: #adadad;\n}\n.ace_optionsMenuEntry button {\n    background: white;\n    border: 1px solid lightgray;\n    margin: 0px;\n}\n.ace_optionsMenuEntry button:hover{\n    background: #f0f0f0;\n}"}),define("ace/ext/menu_tools/overlay_page",["require","exports","module","ace/lib/dom","ace/ext/menu_tools/settings_menu.css"],function(e,t,n){"use strict";var r=e("../../lib/dom"),i=e("./settings_menu.css");r.importCssString(i,"settings_menu.css",!1),n.exports.overlayPage=function(t,n,r){function o(e){e.keyCode===27&&u()}function u(){if(!i)return;document.removeEventListener("keydown",o),i.parentNode.removeChild(i),t&&t.focus(),i=null,r&&r()}function a(e){s=e,e&&(i.style.pointerEvents="none",n.style.pointerEvents="auto")}var i=document.createElement("div"),s=!1;return i.style.cssText="margin: 0; padding: 0; position: fixed; top:0; bottom:0; left:0; right:0;z-index: 9990; "+(t?"background-color: rgba(0, 0, 0, 0.3);":""),i.addEventListener("click",function(e){s||u()}),document.addEventListener("keydown",o),n.addEventListener("click",function(e){e.stopPropagation()}),i.appendChild(n),document.body.appendChild(i),t&&t.blur(),{close:u,setIgnoreFocusOut:a}}}),define("ace/ext/modelist",["require","exports","module"],function(e,t,n){"use strict";function i(e){var t=a.text,n=e.split(/[\/\\]/).pop();for(var i=0;i<r.length;i++)if(r[i].supportsFile(n)){t=r[i];break}return t}var r=[],s=function(){function e(e,t,n){this.name=e,this.caption=t,this.mode="ace/mode/"+e,this.extensions=n;var r;/\^/.test(n)?r=n.replace(/\|(\^)?/g,function(e,t){return"$|"+(t?"^":"^.*\\.")})+"$":r="\\.("+n+")$",this.extRe=new RegExp(r,"gi")}return e.prototype.supportsFile=function(e){return e.match(this.extRe)},e}(),o={ABAP:["abap"],ABC:["abc"],ActionScript:["as"],ADA:["ada|adb"],Alda:["alda"],Apache_Conf:["^htaccess|^htgroups|^htpasswd|^conf|htaccess|htgroups|htpasswd"],Apex:["apex|cls|trigger|tgr"],AQL:["aql"],AsciiDoc:["asciidoc|adoc"],ASL:["dsl|asl|asl.json"],Assembly_ARM32:["s"],Assembly_x86:["asm|a"],Astro:["astro"],AutoHotKey:["ahk"],BatchFile:["bat|cmd"],Basic:["bas|bak"],BibTeX:["bib"],C_Cpp:["cpp|c|cc|cxx|h|hh|hpp|ino"],C9Search:["c9search_results"],Cirru:["cirru|cr"],Clojure:["clj|cljs"],Cobol:["CBL|COB"],coffee:["coffee|cf|cson|^Cakefile"],ColdFusion:["cfm|cfc"],Crystal:["cr"],CSharp:["cs"],Csound_Document:["csd"],Csound_Orchestra:["orc"],Csound_Score:["sco"],CSS:["css"],CSV:["csv"],Curly:["curly"],Cuttlefish:["conf"],D:["d|di"],Dart:["dart"],Diff:["diff|patch"],Django:["djt|html.djt|dj.html|djhtml"],Dockerfile:["^Dockerfile"],Dot:["dot"],Drools:["drl"],Edifact:["edi"],Eiffel:["e|ge"],EJS:["ejs"],Elixir:["ex|exs"],Elm:["elm"],Erlang:["erl|hrl"],Flix:["flix"],Forth:["frt|fs|ldr|fth|4th"],Fortran:["f|f90"],FSharp:["fsi|fs|ml|mli|fsx|fsscript"],FSL:["fsl"],FTL:["ftl"],Gcode:["gcode"],Gherkin:["feature"],Gitignore:["^.gitignore"],Glsl:["glsl|frag|vert"],Gobstones:["gbs"],golang:["go"],GraphQLSchema:["gql"],Groovy:["groovy"],HAML:["haml"],Handlebars:["hbs|handlebars|tpl|mustache"],Haskell:["hs"],Haskell_Cabal:["cabal"],haXe:["hx"],Hjson:["hjson"],HTML:["html|htm|xhtml|we|wpy"],HTML_Elixir:["eex|html.eex"],HTML_Ruby:["erb|rhtml|html.erb"],INI:["ini|conf|cfg|prefs"],Io:["io"],Ion:["ion"],Jack:["jack"],Jade:["jade|pug"],Java:["java"],JavaScript:["js|jsm|cjs|mjs"],JEXL:["jexl"],JSON:["json"],JSON5:["json5"],JSONiq:["jq"],JSP:["jsp"],JSSM:["jssm|jssm_state"],JSX:["jsx"],Julia:["jl"],Kotlin:["kt|kts"],LaTeX:["tex|latex|ltx|bib"],Latte:["latte"],LESS:["less"],Liquid:["liquid"],Lisp:["lisp"],LiveScript:["ls"],Log:["log"],LogiQL:["logic|lql"],Logtalk:["lgt"],LSL:["lsl"],Lua:["lua"],LuaPage:["lp"],Lucene:["lucene"],Makefile:["^Makefile|^GNUmakefile|^makefile|^OCamlMakefile|make"],Markdown:["md|markdown"],Mask:["mask"],MATLAB:["matlab"],Maze:["mz"],MediaWiki:["wiki|mediawiki"],MEL:["mel"],MIPS:["s|asm"],MIXAL:["mixal"],MUSHCode:["mc|mush"],MySQL:["mysql"],Nasal:["nas"],Nginx:["nginx|conf"],Nim:["nim"],Nix:["nix"],NSIS:["nsi|nsh"],Nunjucks:["nunjucks|nunjs|nj|njk"],ObjectiveC:["m|mm"],OCaml:["ml|mli"],Odin:["odin"],PartiQL:["partiql|pql"],Pascal:["pas|p"],Perl:["pl|pm"],pgSQL:["pgsql"],PHP:["php|inc|phtml|shtml|php3|php4|php5|phps|phpt|aw|ctp|module"],PHP_Laravel_blade:["blade.php"],Pig:["pig"],PLSQL:["plsql"],Powershell:["ps1"],Praat:["praat|praatscript|psc|proc"],Prisma:["prisma"],Prolog:["plg|prolog"],Properties:["properties"],Protobuf:["proto"],PRQL:["prql"],Puppet:["epp|pp"],Python:["py"],QML:["qml"],R:["r"],Raku:["raku|rakumod|rakutest|p6|pl6|pm6"],Razor:["cshtml|asp"],RDoc:["Rd"],Red:["red|reds"],RHTML:["Rhtml"],Robot:["robot|resource"],RST:["rst"],Ruby:["rb|ru|gemspec|rake|^Guardfile|^Rakefile|^Gemfile"],Rust:["rs"],SaC:["sac"],SASS:["sass"],SCAD:["scad"],Scala:["scala|sbt"],Scheme:["scm|sm|rkt|oak|scheme"],Scrypt:["scrypt"],SCSS:["scss"],SH:["sh|bash|^.bashrc"],SJS:["sjs"],Slim:["slim|skim"],Smarty:["smarty|tpl"],Smithy:["smithy"],snippets:["snippets"],Soy_Template:["soy"],Space:["space"],SPARQL:["rq"],SQL:["sql"],SQLServer:["sqlserver"],Stylus:["styl|stylus"],SVG:["svg"],Swift:["swift"],Tcl:["tcl"],Terraform:["tf","tfvars","terragrunt"],Tex:["tex"],Text:["txt"],Textile:["textile"],Toml:["toml"],TSV:["tsv"],TSX:["tsx"],Turtle:["ttl"],Twig:["twig|swig"],Typescript:["ts|mts|cts|typescript|str"],Vala:["vala"],VBScript:["vbs|vb"],Velocity:["vm"],Verilog:["v|vh|sv|svh"],VHDL:["vhd|vhdl"],Visualforce:["vfp|component|page"],Vue:["vue"],Wollok:["wlk|wpgm|wtest"],XML:["xml|rdf|rss|wsdl|xslt|atom|mathml|mml|xul|xbl|xaml"],XQuery:["xq"],YAML:["yaml|yml"],Zeek:["zeek|bro"],Zig:["zig"]},u={ObjectiveC:"Objective-C",CSharp:"C#",golang:"Go",C_Cpp:"C and C++",Csound_Document:"Csound Document",Csound_Orchestra:"Csound",Csound_Score:"Csound Score",coffee:"CoffeeScript",HTML_Ruby:"HTML (Ruby)",HTML_Elixir:"HTML (Elixir)",FTL:"FreeMarker",PHP_Laravel_blade:"PHP (Blade Template)",Perl6:"Perl 6",AutoHotKey:"AutoHotkey / AutoIt"},a={};for(var f in o){var l=o[f],c=(u[f]||f).replace(/_/g," "),h=f.toLowerCase(),p=new s(h,c,l[0]);a[h]=p,r.push(p)}n.exports={getModeForPath:i,modes:r,modesByName:a}}),define("ace/ext/prompt",["require","exports","module","ace/config","ace/range","ace/lib/dom","ace/autocomplete","ace/autocomplete/popup","ace/autocomplete/popup","ace/undomanager","ace/tokenizer","ace/ext/menu_tools/overlay_page","ace/ext/modelist"],function(e,t,n){"use strict";function d(e,t,n,r){function b(){var e;m&&m.getCursorPosition().row>0?e=x():e=o.getValue();var t=m?m.getData(m.getRow()):e;t&&!t.error&&(E(),n.onAccept&&n.onAccept({value:e,item:t},o))}function E(){v.close(),r&&r(),p=null}function S(){if(n.getCompletions){var e;n.getPrefix&&(e=n.getPrefix(o));var t=n.getCompletions(o);m.setData(t,e),m.resize(!0)}}function x(){var e=m.getData(m.getRow());if(e&&!e.error)return e.value||e.caption||e}if(typeof t=="object")return d(e,"",t,n);if(p){var i=p;e=i.editor,i.close();if(i.name&&i.name==n.name)return}if(n.$type)return d[n.$type](e,r);var o=a();o.session.setUndoManager(new f);var h=s.buildDom(["div",{"class":"ace_prompt_container"+(n.hasDescription?" input-box-with-description":"")}]),v=c(e,h,E);h.appendChild(o.container),e&&(e.cmdLine=o,o.setOption("fontSize",e.getOption("fontSize"))),t&&o.setValue(t,1),n.selection&&o.selection.setRange({start:o.session.doc.indexToPosition(n.selection[0]),end:o.session.doc.indexToPosition(n.selection[1])});if(n.getCompletions){var m=new u;m.renderer.setStyle("ace_autocomplete_inline"),m.container.style.display="block",m.container.style.maxWidth="600px",m.container.style.width="100%",m.container.style.marginTop="3px",m.renderer.setScrollMargin(2,2,0,0),m.autoSelect=!1,m.renderer.$maxLines=15,m.setRow(-1),m.on("click",function(e){var t=m.getData(m.getRow());t.error||(o.setValue(t.value||t.name||t),b(),e.stop())}),h.appendChild(m.container),S()}if(n.$rules){var g=new l(n.$rules);o.session.bgTokenizer.setTokenizer(g)}n.placeholder&&o.setOption("placeholder",n.placeholder);if(n.hasDescription){var y=s.buildDom(["div",{"class":"ace_prompt_text_container"}]);s.buildDom(n.prompt||"Press 'Enter' to confirm or 'Escape' to cancel",y),h.appendChild(y)}v.setIgnoreFocusOut(n.ignoreFocusOut);var w={Enter:b,"Esc|Shift-Esc":function(){n.onCancel&&n.onCancel(o.getValue(),o),E()}};m&&Object.assign(w,{Up:function(e){m.goTo("up"),x()},Down:function(e){m.goTo("down"),x()},"Ctrl-Up|Ctrl-Home":function(e){m.goTo("start"),x()},"Ctrl-Down|Ctrl-End":function(e){m.goTo("end"),x()},Tab:function(e){m.goTo("down"),x()},PageUp:function(e){m.gotoPageUp(),x()},PageDown:function(e){m.gotoPageDown(),x()}}),o.commands.bindKeys(w),o.on("input",function(){n.onInput&&n.onInput(),S()}),o.resize(!0),m&&m.resize(!0),o.focus(),p={close:E,name:n.name,editor:e}}var r=e("../config").nls,i=e("../range").Range,s=e("../lib/dom"),o=e("../autocomplete").FilteredList,u=e("../autocomplete/popup").AcePopup,a=e("../autocomplete/popup").$singleLineEditor,f=e("../undomanager").UndoManager,l=e("../tokenizer").Tokenizer,c=e("./menu_tools/overlay_page").overlayPage,h=e("./modelist"),p;d.gotoLine=function(e,t){function n(e){return Array.isArray(e)||(e=[e]),e.map(function(e){var t=e.isBackwards?e.start:e.end,n=e.isBackwards?e.end:e.start,r=n.row,i=r+1+":"+n.column;return n.row==t.row?n.column!=t.column&&(i+=">:"+t.column):i+=">"+(t.row+1)+":"+t.column,i}).reverse().join(", ")}d(e,":"+n(e.selection.toJSON()),{name:"gotoLine",selection:[1,Number.MAX_VALUE],onAccept:function(t){var n=t.value,r=d.gotoLine._history;r||(d.gotoLine._history=r=[]),r.indexOf(n)!=-1&&r.splice(r.indexOf(n),1),r.unshift(n),r.length>20&&(r.length=20);var s=e.getCursorPosition(),o=[];n.replace(/^:/,"").split(/,/).map(function(t){function u(){var t=n[r++];if(!t)return;if(t[0]=="c"){var i=parseInt(t.slice(1))||0;return e.session.doc.indexToPosition(i)}var o=s.row,u=0;return/\d/.test(t)&&(o=parseInt(t)-1,t=n[r++]),t==":"&&(t=n[r++],/\d/.test(t)&&(u=parseInt(t)||0)),{row:o,column:u}}var n=t.split(/([<>:+-]|c?\d+)|[^c\d<>:+-]+/).filter(Boolean),r=0;s=u();var a=i.fromPoints(s,s);n[r]==">"?(r++,a.end=u()):n[r]=="<"&&(r++,a.start=u()),o.unshift(a)}),e.selection.fromJSON(o);var u=e.renderer.scrollTop;e.renderer.scrollSelectionIntoView(e.selection.anchor,e.selection.cursor,.5),e.renderer.animateScrolling(u)},history:function(){return d.gotoLine._history?d.gotoLine._history:[]},getCompletions:function(t){var n=t.getValue(),r=n.replace(/^:/,"").split(":"),i=Math.min(parseInt(r[0])||1,e.session.getLength())-1,s=e.session.getLine(i),o=n+"  "+s;return[o].concat(this.history())},$rules:{start:[{regex:/\d+/,token:"string"},{regex:/[:,><+\-c]/,token:"keyword"}]}})},d.commands=function(e,t){function n(e){return(e||"").replace(/^./,function(e){return e.toUpperCase(e)}).replace(/[a-z][A-Z]/g,function(e){return e[0]+" "+e[1].toLowerCase(e)})}function i(t){var r=[],i={};return e.keyBinding.$handlers.forEach(function(e){var s=e.platform,o=e.byName;for(var u in o){var a=o[u].bindKey;typeof a!="string"&&(a=a&&a[s]||"");var f=o[u],l=f.description||n(f.name);Array.isArray(f)||(f=[f]),f.forEach(function(e){typeof e!="string"&&(e=e.name);var n=t.find(function(t){return t===e});n||(i[e]?i[e].key+="|"+a:(i[e]={key:a,command:e,description:l},r.push(i[e])))})}}),r}var s=["insertstring","inserttext","setIndentation","paste"],u=i(s);u=u.map(function(e){return{value:e.description,meta:e.key,command:e.command}}),d(e,"",{name:"commands",selection:[0,Number.MAX_VALUE],maxHistoryCount:5,onAccept:function(t){if(t.item){var n=t.item.command;this.addToHistory(t.item),e.execCommand(n)}},addToHistory:function(e){var t=this.history();t.unshift(e),delete e.message;for(var n=1;n<t.length;n++)if(t[n]["command"]==e.command){t.splice(n,1);break}this.maxHistoryCount>0&&t.length>this.maxHistoryCount&&t.splice(t.length-1,1),d.commands.history=t},history:function(){return d.commands.history||[]},getPrefix:function(e){var t=e.getCursorPosition(),n=e.getValue();return n.substring(0,t.column)},getCompletions:function(e){function t(e,t){var n=JSON.parse(JSON.stringify(e)),r=new o(n);return r.filterCompletions(n,t)}function n(e,t){if(!t||!t.length)return e;var n=[];t.forEach(function(e){n.push(e.command)});var r=[];return e.forEach(function(e){n.indexOf(e.command)===-1&&r.push(e)}),r}var i=this.getPrefix(e),s=t(this.history(),i),a=n(u,s);a=t(a,i),s.length&&a.length&&(s[0].message=r("prompt.recently-used","Recently used"),a[0].message=r("prompt.other-commands","Other commands"));var f=s.concat(a);return f.length>0?f:[{value:r("prompt.no-matching-commands","No matching commands"),error:1}]}})},d.modes=function(e,t){var n=h.modes;n=n.map(function(e){return{value:e.caption,mode:e.name}}),d(e,"",{name:"modes",selection:[0,Number.MAX_VALUE],onAccept:function(t){if(t.item){var n="ace/mode/"+t.item.mode;e.session.setMode(n)}},getPrefix:function(e){var t=e.getCursorPosition(),n=e.getValue();return n.substring(0,t.column)},getCompletions:function(e){function t(e,t){var n=JSON.parse(JSON.stringify(e)),r=new o(n);return r.filterCompletions(n,t)}var r=this.getPrefix(e),i=t(n,r);return i.length>0?i:[{caption:"No mode matching",value:"No mode matching",error:1}]}})},s.importCssString(".ace_prompt_container {\n    max-width: 603px;\n    width: 100%;\n    margin: 20px auto;\n    padding: 3px;\n    background: white;\n    border-radius: 2px;\n    box-shadow: 0px 2px 3px 0px #555;\n}","promtp.css",!1),t.prompt=d});                (function() {
                    window.require(["ace/ext/prompt"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            