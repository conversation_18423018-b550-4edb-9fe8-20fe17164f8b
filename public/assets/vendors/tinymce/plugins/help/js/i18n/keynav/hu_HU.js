tinymce.Resource.add('tinymce.html-i18n.help-keynav.hu_HU',
'<h1><PERSON><PERSON><PERSON><PERSON>zetes navigáció indítása</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Fókusz a menüsávra</dt>\n' +
  '  <dd>Windows és Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Fókusz az eszköztárra</dt>\n' +
  '  <dd>Windows és Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Fókusz a láblécre</dt>\n' +
  '  <dd>Windows és Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Ráközelítés az értesítésre</dt>\n' +
  '  <dd>Windows vagy Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Fókusz egy környezetfüggő eszköztárra</dt>\n' +
  '  <dd>Windows, Linux és macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>A navigáció az első felhasználói felületi elemnél kezdődik, amelyet a rendszer kiemel, illetve aláhúz, amennyiben az az első elem\n' +
  '  a lábléc elemútvonalán.</p>\n' +
  '\n' +
  '<h1>Navigálás a felhasználói felület szakaszai között</h1>\n' +
  '\n' +
  '<p>A felhasználói felület következő szakaszára váltáshoz nyomja meg a <strong>Tab</strong> billentyűt.</p>\n' +
  '\n' +
  '<p>A felhasználói felület előző szakaszára váltáshoz nyomja meg a <strong>Shift+Tab</strong> billentyűt.</p>\n' +
  '\n' +
  '<p>A <strong>Tab</strong> billentyűvel a felhasználói felület szakaszai között a következő sorrendben vált:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Menüsáv</li>\n' +
  '  <li>Az egyes eszköztárcsoportok</li>\n' +
  '  <li>Oldalsáv</li>\n' +
  '  <li>Elemútvonal a láblécen</li>\n' +
  '  <li>Szószámátkapcsoló gomb a láblécen</li>\n' +
  '  <li>Márkalink a láblécen</li>\n' +
  '  <li>Szerkesztő átméretezési fogópontja a láblécen</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Ha a felhasználói felület valamelyik eleme nincs jelen, a rendszer kihagyja.</p>\n' +
  '\n' +
  '<p>Ha a billentyűzetes navigáció fókusza a láblécen van, és nincs látható oldalsáv, a <strong>Shift+Tab</strong>\n' +
  '  billentyűkombináció lenyomásakor az első eszköztárcsoportra ugrik a fókusz, nem az utolsóra.</p>\n' +
  '\n' +
  '<h1>Navigálás a felhasználói felület szakaszain belül</h1>\n' +
  '\n' +
  '<p>A felhasználói felület következő elemére váltáshoz nyomja meg a megfelelő <strong>nyílbillentyűt</strong>.</p>\n' +
  '\n' +
  '<p>A <strong>bal</strong> és a <strong>jobb</strong> nyílgomb</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>a menüsávban a menük között vált.</li>\n' +
  '  <li>a menükben megnyit egy almenüt.</li>\n' +
  '  <li>az eszköztárcsoportban a gombok között vált.</li>\n' +
  '  <li>a lábléc elemútvonalán az elemek között vált.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>A <strong>le</strong> és a <strong>fel</strong> nyílgomb</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>a menükben a menüpontok között vált.</li>\n' +
  '  <li>az eszköztár előugró menüjében az elemek között vált.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>A <strong>nyílbillentyűk</strong> lenyomásával körkörösen lépkedhet a fókuszban lévő felhasználói felületi szakasz elemei között.</p>\n' +
  '\n' +
  '<p>A megnyitott menüket, almenüket és előugró menüket az <strong>Esc</strong> billentyűvel zárhatja be.</p>\n' +
  '\n' +
  '<p>Ha a fókusz az aktuális felületi elem „felső” részén van, az <strong>Esc</strong> billentyűvel az egész\n' +
  '  billentyűzetes navigációból kilép.</p>\n' +
  '\n' +
  '<h1>Menüpont vagy eszköztárgomb aktiválása</h1>\n' +
  '\n' +
  '<p>Amikor a kívánt menüelem vagy eszköztárgomb van kijelölve, nyomja meg a <strong>Return</strong>, az <strong>Enter</strong>\n' +
  '  vagy a <strong>Szóköz</strong> billentyűt az adott elem vagy gomb aktiválásához.</p>\n' +
  '\n' +
  '<h1>Navigálás a lapokkal nem rendelkező párbeszédablakokban</h1>\n' +
  '\n' +
  '<p>A lapokkal nem rendelkező párbeszédablakokban az első interaktív összetevő kapja a fókuszt, amikor a párbeszédpanel megnyílik.</p>\n' +
  '\n' +
  '<p>A párbeszédpanelek interaktív összetevői között a <strong>Tab</strong> vagy a <strong>Shift+Tab</strong> billentyűvel navigálhat.</p>\n' +
  '\n' +
  '<h1>Navigálás a lapokkal rendelkező párbeszédablakokban</h1>\n' +
  '\n' +
  '<p>A lapokkal rendelkező párbeszédablakokban a lapmenü első gombja kapja a fókuszt, amikor a párbeszédpanel megnyílik.</p>\n' +
  '\n' +
  '<p>A párbeszédpanel e lapjának interaktív összetevői között a <strong>Tab</strong> vagy\n' +
  '  <strong>Shift+Tab</strong> billentyűvel navigálhat.</p>\n' +
  '\n' +
  '<p>A párbeszédablak másik lapjára úgy léphet, hogy a fókuszt a lapmenüre állítja, majd lenyomja a megfelelő <strong>nyílbillentyűt</strong>\n' +
  '  a rendelkezésre álló lapok közötti lépkedéshez.</p>\n');