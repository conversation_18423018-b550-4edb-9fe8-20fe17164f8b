tinymce.Resource.add('tinymce.html-i18n.help-keynav.he_IL',
'<h1>התחל ניווט במקלדת</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>התמקד בשורת התפריטים</dt>\n' +
  '  <dd>Windows או Linux:‏ Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>העבר מיקוד לסרגל הכלים</dt>\n' +
  '  <dd>Windows או Linux:‏ Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>העבר מיקוד לכותרת התחתונה</dt>\n' +
  '  <dd>Windows או Linux:‏ Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>העבר מיקוד להודעה</dt>\n' +
  '  <dd>Windows או Linux:‏ Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>העבר מיקוד לסרגל כלים הקשרי</dt>\n' +
  '  <dd>Windows‏, Linux או macOS:‏ Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>הניווט יתחיל ברכיב הראשון במשך, שיודגש או שיהיה מתחתיו קו תחתון במקרה של הפריט הראשון\n' +
  '  הנתיב של רכיב הכותרת התחתונה.</p>\n' +
  '\n' +
  '<h1>עבור בין מקטעים במסך</h1>\n' +
  '\n' +
  '<p>כדי לעבור בין המקטעים במסך, הקש <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>כדי לעבור למקטע הקודם במסך, הקש <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>הסדר מבחינת מקש <strong>Tab</strong> של הרכיבים במסך:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>שורת התפריטים</li>\n' +
  '  <li>כל קבוצה בסרגל הכלים</li>\n' +
  '  <li>הסרגל הצידי</li>\n' +
  '  <li>נתיב של רכיב בכותרת התחתונה</li>\n' +
  '  <li>לחצן לספירת מילים בכותרת התחתונה</li>\n' +
  '  <li>קישור של המותג בכותרת התחתונה</li>\n' +
  '  <li>ידית לשינוי גודל עבור העורך בכותרת התחתונה</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>אם רכיב כלשהו במסך לא מופיע, המערכת תדלג עליו.</p>\n' +
  '\n' +
  '<p>אם בכותרת התחתונה יש מיקוד של ניווט במקלדת, ולא מופיע סרגל בצד, יש להקיש <strong>Shift+Tab</strong>\n' +
  '  מעביר את המיקוד לקבוצה הראשונה בסרגל הכלים, לא האחרונה.</p>\n' +
  '\n' +
  '<h1>עבור בתוך מקטעים במסך</h1>\n' +
  '\n' +
  '<p>כדי לעבור מרכיב אחד לרכיב אחר במסך, הקש על מקש <strong>החץ</strong> המתאים.</p>\n' +
  '\n' +
  '<p>מקשי החיצים <strong>שמאלה</strong> ו<strong>ימינה</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>עבור בין תפריטים בשורת התפריטים.</li>\n' +
  '  <li>פתח תפריט משני בתפריט.</li>\n' +
  '  <li>עבור בין לחצנים בקבוצה בסרגל הכלים.</li>\n' +
  '  <li>עבור בין פריטים ברכיב בכותרת התחתונה.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>מקשי החיצים <strong>למטה</strong> ו<strong>למעלה</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>עבור בין פריטים בתפריט.</li>\n' +
  '  <li>עבור בין פריטים בחלון הקובץ של סרגל הכלים.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>מקשי <strong>החצים</strong> משתנים בתוך המקטע במסך שעליו נמצא המיקוד.</p>\n' +
  '\n' +
  '<p>כדי לסגור תפריט פתוח, תפריט משני פתוח או חלון קופץ, הקש על <strong>Esc</strong>.</p>\n' +
  '\n' +
  "<p>אם המיקוד הוא על החלק 'העליון' של מקטע מסוים במסך, הקשה על <strong>Esc</strong> מביאה גם ליציאה\n" +
  '  מהניווט במקלדת לחלוטין.</p>\n' +
  '\n' +
  '<h1>הפעל פריט בתפריט או לחצן בסרגל הכלים</h1>\n' +
  '\n' +
  '<p>כאשר הפריט הרצוי בתפריט או הלחצן בסרגל הכלים מודגשים, הקש על <strong>Return</strong>, <strong>Enter</strong>,\n' +
  '  או על <strong>מקש הרווח</strong> כדי להפעיל את הפריט.</p>\n' +
  '\n' +
  '<h1>ניווט בחלונות דו-שיח בלי כרטיסיות</h1>\n' +
  '\n' +
  '<p>בחלונות דו-שיח בלי כרטיסיות, הרכיב האינטראקטיבי הראשון מקבל את המיקוד כאשר החלון נפתח.</p>\n' +
  '\n' +
  '<p>עבור בין רכיבים אינטראקטיביים בחלון על ידי הקשה על <strong>Tab</strong> או <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>ניווט בחלונות דו-שיח עם כרטיסיות</h1>\n' +
  '\n' +
  '<p>בחלונות דו-שיח עם כרטיסיות, הלחצן הראשון בתפריט מקבל את המיקוד כאשר החלון נפתח.</p>\n' +
  '\n' +
  '<p>עבור בין רכיבים אינטראקטיביים בחלון על ידי הקשה על <strong>Tab</strong> או\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>עבור לכרטיסיה אחרת בחלון על ידי העברת המיקוד לתפריט הכרטיסיות והקשה על <strong>החץ</strong>המתאים\n' +
  '  כדי לעבור בין הכרטיסיות הזמינות.</p>\n');