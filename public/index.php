<?php

/**
 * Multi-tenant application entry point
 * Handles all incoming requests and routes them appropriately
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Start output buffering
ob_start();

// Set error reporting based on environment
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Europe/Istanbul');

// Include core classes
require_once __DIR__ . '/../core/Environment.php';
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Middleware.php';
require_once __DIR__ . '/../core/Router.php';
require_once __DIR__ . '/../core/Auth.php';
require_once __DIR__ . '/../core/DynamicAdminPanel.php';
require_once __DIR__ . '/../core/TenantCreator.php';

// Load environment variables
Environment::load();

// Load configuration
$config = require __DIR__ . '/../config/app.php';

try {
    // Initialize middleware
    $middleware = new TenantMiddleware();
    
    // Make middleware globally accessible
    $GLOBALS['middleware'] = $middleware;
    
    // Handle the request through middleware
    $middleware->handle($_SERVER);
    
    // Initialize router with middleware context
    $router = new Router($middleware);
    
    // Get current request URI and method
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    // Handle CORS for API requests
    if ($requestMethod === 'OPTIONS') {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        http_response_code(200);
        exit;
    }
    
    // Set security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    
    // Force HTTPS if configured
    if ($config['force_https'] && !isset($_SERVER['HTTPS'])) {
        $redirectUrl = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        header("Location: {$redirectUrl}", true, 301);
        exit;
    }
    
    // Resolve and execute the route
    $router->resolve($requestUri, $requestMethod);
    
} catch (Exception $e) {
    // Log the error
    error_log("Application error: " . $e->getMessage());
    
    // Handle different types of errors
    $statusCode = $e->getCode() ?: 500;
    
    switch ($statusCode) {
        case 404:
            http_response_code(404);
            showErrorPage(404, 'Sayfa Bulunamadı', 'Aradığınız sayfa bulunamadı.');
            break;
            
        case 403:
            http_response_code(403);
            showErrorPage(403, 'Erişim Engellendi', 'Bu sayfaya erişim yetkiniz bulunmamaktadır.');
            break;
            
        case 500:
        default:
            http_response_code(500);
            if ($config['debug']) {
                showErrorPage(500, 'Sistem Hatası', $e->getMessage());
            } else {
                showErrorPage(500, 'Sistem Hatası', 'Bir sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.');
            }
            break;
    }
}

// End output buffering and send output
ob_end_flush();

/**
 * Show error page
 * 
 * @param int $code
 * @param string $title
 * @param string $message
 */
function showErrorPage(int $code, string $title, string $message): void
{
    ?>
    <!DOCTYPE html>
    <html lang="tr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo $code; ?> - <?php echo htmlspecialchars($title); ?></title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 0;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                background: white;
                padding: 3rem;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                max-width: 500px;
                margin: 2rem;
            }
            .error-code {
                font-size: 4rem;
                font-weight: bold;
                color: #e53e3e;
                margin-bottom: 1rem;
            }
            .error-title {
                font-size: 1.5rem;
                color: #2d3748;
                margin-bottom: 1rem;
            }
            .error-message {
                color: #4a5568;
                margin-bottom: 2rem;
                line-height: 1.6;
            }
            .back-button {
                display: inline-block;
                background: #667eea;
                color: white;
                padding: 0.75rem 1.5rem;
                text-decoration: none;
                border-radius: 5px;
                transition: background 0.3s;
            }
            .back-button:hover {
                background: #5a67d8;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-code"><?php echo $code; ?></div>
            <div class="error-title"><?php echo htmlspecialchars($title); ?></div>
            <div class="error-message"><?php echo htmlspecialchars($message); ?></div>
            <a href="/" class="back-button">Anasayfaya Dön</a>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Auto-load controller classes
 * 
 * @param string $className
 */
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . "/../controllers/{$className}.php",
        __DIR__ . "/../admin/tenant/controllers/{$className}.php",
        __DIR__ . "/../admin/central/controllers/{$className}.php",
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

/**
 * Helper function to get current tenant information
 * 
 * @return array|null
 */
function getCurrentTenant(): ?array
{
    return defined('CURRENT_TENANT') ? CURRENT_TENANT : null;
}

/**
 * Helper function to get current theme configuration
 * 
 * @return array|null
 */
function getThemeConfig(): ?array
{
    return defined('THEME_CONFIG') ? THEME_CONFIG : null;
}

/**
 * Helper function to get tenant database name
 * 
 * @return string|null
 */
function getTenantDbName(): ?string
{
    return defined('TENANT_DB_NAME') ? TENANT_DB_NAME : null;
}

/**
 * Helper function to check if current request is for central admin
 * 
 * @return bool
 */
function isCentralAdmin(): bool
{
    return defined('IS_CENTRAL_ADMIN') && IS_CENTRAL_ADMIN;
}

/**
 * Helper function to get text (translation system disabled)
 * 
 * @param string $key
 * @return string
 */
function translate(string $key): string
{
    // Translation system disabled - return key as is
    return $key;
}

/**
 * Helper function to get site setting
 * 
 * @param string $key
 * @param mixed $default
 * @return mixed
 */
function getSetting(string $key, $default = null)
{
    if (!getTenantDbName()) {
        return $default;
    }
    
    try {
        $database = new Database();
        
        $query = "SELECT setting_value FROM settings WHERE setting_key = ?";
        $stmt = $database->queryTenant(getTenantDbName(), $query, [$key]);
        $result = $stmt->fetch();
        
        return $result ? $result['setting_value'] : $default;
        
    } catch (Exception $e) {
        error_log("Setting error: " . $e->getMessage());
        return $default;
    }
}

/**
 * Helper function to generate URL
 * 
 * @param string $path
 * @param array $params
 * @return string
 */
function url(string $path = '', array $params = []): string
{
    $baseUrl = $config['base_url'] ?? 'http://localhost';
    $url = rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * Helper function to redirect
 * 
 * @param string $url
 * @param int $statusCode
 */
function redirect(string $url, int $statusCode = 302): void
{
    header("Location: {$url}", true, $statusCode);
    exit;
}

/**
 * Helper function to sanitize input
 * 
 * @param mixed $input
 * @return mixed
 */
function sanitize($input)
{
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}