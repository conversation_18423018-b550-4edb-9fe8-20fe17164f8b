# Multi-Tenant SaaS Application

PHP 8.4 tabanlı çoklu kiracı (multi-tenant) SaaS uygulaması. Her kiracı kendi veritabanında çalışır ve dinamik tema sistemi ile farklı sektörlere hizmet verir.

## 🚀 Özellikler

- **Multi-Tenant Yapı**: Her site kendi veritabanında çalışır
- **Dinamik Tema Sistemi**: Sektöre özel temalar (Avukat, Diyetisyen, Teknik Servis)
- **Çoklu Dil Desteği**: Site bazlı dil yönetimi
- **Dinamik Admin Panel**: Tema yapılandırmasına göre otomatik admin paneli
- **Güvenlik**: CSRF koruması, SQL Injection koruması, XSS koruması
- **Responsive Design**: Mobil uyumlu arayüz

## 📋 Gereksinimler

- PHP 8.4+
- MySQL 8.0+
- Apache/Nginx
- Composer (opsiyonel)

## 🛠️ Local Development Setup (Herd)

### 1. <PERSON><PERSON><PERSON>
```bash
git clone <repository-url>
cd php-kurumsal
```

### 2. Environment Ayarları
`.env` dosyası zaten hazır. Gerekirse veritabanı bilgilerini düzenle:
```env
CENTRAL_DB_HOST=127.0.0.1
CENTRAL_DB_NAME=central_db
CENTRAL_DB_USER=root
CENTRAL_DB_PASS=
```

### 3. Veritabanı ve Tenant Setup
```bash
php setup.php
```

Bu komut:
- Merkezi veritabanını oluşturur
- Örnek tenant'ları oluşturur (avukat.test, diyetisyen.test, tekniker.test)
- Admin kullanıcılarını oluşturur

### 4. Domain Yönlendirme

#### Seçenek A: /etc/hosts Düzenleme
```bash
sudo nano /etc/hosts
```
Şu satırları ekle:
```
127.0.0.1 central.test
127.0.0.1 avukat.test
127.0.0.1 diyetisyen.test
127.0.0.1 tekniker.test
```

#### Seçenek B: Herd Site Linking
```bash
cd /path/to/php-kurumsal/public
herd link central
herd link avukat
herd link diyetisyen
herd link tekniker
```

### 5. Erişim URL'leri

**Merkezi Admin:**
- URL: http://central.test/central-admin
- Kullanıcı: admin
- Şifre: admin123

**Avukat Sitesi:**
- Site: http://avukat.test
- Admin: http://avukat.test/admin
- Kullanıcı: admin / admin123

**Diyetisyen Sitesi:**
- Site: http://diyetisyen.test
- Admin: http://diyetisyen.test/admin
- Kullanıcı: admin / admin123

**Tekniker Sitesi:**
- Site: http://tekniker.test
- Admin: http://tekniker.test/admin
- Kullanıcı: admin / admin123

## 🗄️ Veritabanı Yapısı

### Merkezi Veritabanı (central_db)
- `themes`: Mevcut temalar
- `tenants`: Kiracı bilgileri
- `super_admin_users`: Merkezi admin kullanıcıları

### Kiracı Veritabanları
Her kiracı için ayrı veritabanı:
- `avukat_test`: Avukat sitesi veritabanı
- `diyetisyen_test`: Diyetisyen sitesi veritabanı
- `tekniker_test`: Tekniker sitesi veritabanı

Her kiracı veritabanında:
- Ortak tablolar: `admin_users`, `languages`, `translations`, `pages`, `settings`, `blog_posts`, `categories`, `media`
- Tema özel tablolar: Tema yapılandırmasına göre

## 🎨 Tema Sistemi

### Mevcut Temalar
1. **lawyer1**: Çoklu avukat teması
2. **lawyer2**: Bireysel avukat teması
3. **dietitian1**: Diyetisyen teması
4. **technical1**: Teknik servis teması

### Yeni Tema Ekleme
1. `themes/yeni-tema/` klasörü oluştur
2. `themes/yeni-tema/config.php` dosyasını yapılandır
3. `migrations/yeni-tema.sql` migration dosyası oluştur
4. Merkezi veritabanına tema kaydını ekle

## 🌍 Çoklu Dil Sistemi

- Her kiracı kendi dillerini yönetir
- Varsayılan dil: Türkçe
- URL yapısı: `site.com/sayfa` (varsayılan) ve `site.com/sayfa?lang=en`
- Çeviriler veritabanında tutulur

## 🔧 Yeni Kiracı Ekleme

### Manuel Ekleme
```php
require_once 'core/TenantCreator.php';

$tenantCreator = new TenantCreator();
$result = $tenantCreator->createTenant(
    'yeni-site.test',  // domain
    1,                 // theme_id
    null,              // expired_at
    'admin',           // admin_username
    '<EMAIL>', // admin_email
    'admin123'         // admin_password
);
```

### Merkezi Admin Üzerinden
1. http://central.test/central-admin adresine git
2. Tenants bölümünden "Yeni Kiracı Ekle"
3. Domain, tema ve admin bilgilerini gir

## 📁 Proje Yapısı

```
php-kurumsal/
├── config/           # Yapılandırma dosyaları
├── core/            # Ana sistem sınıfları
├── themes/          # Tema dosyaları
├── migrations/      # Veritabanı migration'ları
├── admin/           # Admin panel dosyaları
├── public/          # Web erişilebilir dosyalar
├── .env            # Environment değişkenleri
├── setup.php       # Kurulum scripti
└── README.md       # Bu dosya
```

## 🔒 Güvenlik

- CSRF token koruması
- SQL Injection koruması (PDO prepared statements)
- XSS koruması (input sanitization)
- Session güvenliği
- File upload güvenliği
- Rate limiting (brute force koruması)

## 🐛 Hata Ayıklama

### Debug Modu
`.env` dosyasında:
```env
APP_DEBUG=true
```

### Log Dosyaları
Hatalar PHP error log'una yazılır.

### Yaygın Sorunlar

**Veritabanı Bağlantı Hatası:**
- MySQL servisinin çalıştığından emin ol
- `.env` dosyasındaki veritabanı bilgilerini kontrol et

**Domain Erişim Sorunu:**
- `/etc/hosts` dosyasını kontrol et
- Herd site linking'i kontrol et

**Permission Hatası:**
- `public/assets/uploads/` klasörü yazılabilir olmalı (755)

## 📝 Geliştirme Notları

- Tüm yorum satırları İngilizce ve PHPDoc standardında
- PSR-4 autoloading kullanılıyor
- MVC pattern takip ediliyor
- Responsive design için Bootstrap 5 kullanılıyor

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Commit yapın (`git commit -am 'Yeni özellik eklendi'`)
4. Branch'i push yapın (`git push origin feature/yeni-ozellik`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.