<?php

/**
 * Multi-tenant application setup script
 * Sets up databases, creates sample tenants for local development
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Include core classes
require_once __DIR__ . '/core/Environment.php';
require_once __DIR__ . '/core/Database.php';
require_once __DIR__ . '/core/Auth.php';
require_once __DIR__ . '/core/TenantCreator.php';

// Load environment variables
Environment::load();

echo "🚀 Multi-Tenant Application Setup\n";
echo "================================\n\n";

try {
    // Initialize database and tenant creator
    $database = new Database();
    $tenantCreator = new TenantCreator();
    
    echo "1. Setting up central database...\n";
    
    // Run central database migration
    $database->runMigration('central_db', 'central_db.sql');
    echo "   ✅ Central database created\n";
    
    echo "\n2. Creating sample tenants for development...\n";
    
    // Sample tenants for development
    $sampleTenants = [
        [
            'domain' => 'avukat.test',
            'theme_id' => 1, // lawyer1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ],
        [
            'domain' => 'a1.com',
            'theme_id' => 1, // lawyer1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ],
        [
            'domain' => 'b1.com',
            'theme_id' => 1, // lawyer1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ],
        [
            'domain' => 'c1.com',
            'theme_id' => 1, // lawyer1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ],
        [
            'domain' => 'diyetisyen.test',
            'theme_id' => 3, // dietitian1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ],
        [
            'domain' => 'tekniker.test',
            'theme_id' => 4, // technical1
            'admin_username' => 'admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123'
        ]
    ];
    
    foreach ($sampleTenants as $tenant) {
        try {
            // Check if tenant already exists
            if ($tenantCreator->getTenantByDomain($tenant['domain'])) {
                echo "   ⚠️  Tenant {$tenant['domain']} already exists, skipping...\n";
                continue;
            }
            
            $result = $tenantCreator->createTenant(
                $tenant['domain'],
                $tenant['theme_id'],
                null, // no expiration for dev
                $tenant['admin_username'],
                $tenant['admin_email'],
                $tenant['admin_password']
            );
            
            echo "   ✅ Created tenant: {$result['domain']} (DB: {$result['database']})\n";
            echo "      Admin: {$result['admin_username']} / {$tenant['admin_password']}\n";
            
        } catch (Exception $e) {
            echo "   ❌ Failed to create {$tenant['domain']}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n3. Setup complete! 🎉\n\n";
    
    echo "📋 Next Steps:\n";
    echo "==============\n";
    echo "1. Add these domains to your /etc/hosts file:\n";
    echo "   127.0.0.1 central.test\n";
    echo "   127.0.0.1 avukat.test\n";
    echo "   127.0.0.1 a1.com\n";
    echo "   127.0.0.1 b1.com\n";
    echo "   127.0.0.1 c1.com\n";
    echo "   127.0.0.1 diyetisyen.test\n";
    echo "   127.0.0.1 tekniker.test\n\n";
    
    echo "2. Or use Herd's site linking:\n";
    echo "   herd link central\n";
    echo "   herd link avukat\n";
    echo "   herd link a1\n";
    echo "   herd link b1\n";
    echo "   herd link c1\n";
    echo "   herd link diyetisyen\n";
    echo "   herd link tekniker\n\n";
    
    echo "3. Access your sites:\n";
    echo "   Central Admin: http://central.test/central-admin\n";
    echo "   Avukat Site: http://avukat.test\n";
    echo "   Avukat Admin: http://avukat.test/admin\n";
    echo "   A1 Lawyer Site: http://a1.com\n";
    echo "   A1 Lawyer Admin: http://a1.com/admin\n";
    echo "   B1 Lawyer Site: http://b1.com\n";
    echo "   B1 Lawyer Admin: http://b1.com/admin\n";
    echo "   C1 Lawyer Site: http://c1.com\n";
    echo "   C1 Lawyer Admin: http://c1.com/admin\n";
    echo "   Diyetisyen Site: http://diyetisyen.test\n";
    echo "   Diyetisyen Admin: http://diyetisyen.test/admin\n";
    echo "   Tekniker Site: http://tekniker.test\n";
    echo "   Tekniker Admin: http://tekniker.test/admin\n\n";
    
    echo "4. Default login credentials:\n";
    echo "   Username: admin\n";
    echo "   Password: admin123\n\n";
    
    echo "🔧 Development Info:\n";
    echo "===================\n";
    echo "Database Host: " . Environment::get('CENTRAL_DB_HOST', '127.0.0.1') . "\n";
    echo "Central DB: " . Environment::get('CENTRAL_DB_NAME', 'central_db') . "\n";
    echo "Tenant DBs: avukat_test, a1_com, b1_com, c1_com, diyetisyen_test, tekniker_test\n\n";
    
} catch (Exception $e) {
    echo "❌ Setup failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

/**
 * Helper function to add tenant manually
 */
function addTenant($domain, $themeId, $username = 'admin', $email = null, $password = 'admin123') {
    $tenantCreator = new TenantCreator();
    $email = $email ?: "admin@{$domain}";
    
    try {
        $result = $tenantCreator->createTenant($domain, $themeId, null, $username, $email, $password);
        echo "✅ Tenant created: {$result['domain']}\n";
        echo "   Database: {$result['database']}\n";
        echo "   Admin: {$result['admin_username']} / {$password}\n";
        return $result;
    } catch (Exception $e) {
        echo "❌ Failed to create tenant: " . $e->getMessage() . "\n";
        return false;
    }
}

// Example usage:
// php setup.php
// 
// To add a new tenant manually:
// $result = addTenant('yeni-site.test', 1, 'admin', '<EMAIL>', 'admin123');