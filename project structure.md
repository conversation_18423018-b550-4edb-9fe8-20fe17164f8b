# Proje Yapısı ve Kurallar

## Genel Kurallar

### Kod Yazım Kuralları
- Projedeki kodlar aşağıdaki yapıya uygun şekilde yazılmalı
- Bu projede PHP8.4 ve PDO kullanılmaktadır
- Tema desteği ve multi tenant yapı bulunmaktadır
- Bu projedeki yorum satırları İngilizce ve PHPDoc'a uygun yazılmalıdır

## Teknik Özellikler

### PHP Sürümü
- **PHP 8.4** kullanılmaktadır

### Veritabanı
- **PDO (PHP Data Objects)** kullanılmaktadır

### Desteklenen Özellikler
- **<PERSON><PERSON> (Theme Support)**
- **Multi Tenant Yapı**
- **Dinamik Admin Panel**

### Yorum Satırları
- Tüm yorum satırları **İngilizce** yazılmalıdır
- **PHPDoc** standardına uygun olmalıdır

## Proje Yapısı

### Veritabanı Yapısı

#### Merkezi DB (central_db)
```sql
-- Tema tablosu
CREATE TABLE themes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tenant tablosu
CREATE TABLE tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    domain VARCHAR(255) UNIQUE NOT NULL,
    theme_id INT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    expired_at DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (theme_id) REFERENCES themes(id)
);

-- Süper admin kullanıcıları
CREATE TABLE super_admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Tenant DB Yapısı (Her domain için ayrı DB)

##### Ortak Tablolar (Tüm sektörlerde)
```sql
-- Admin kullanıcıları
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Language tables removed - Multi-language support disabled

-- Sayfalar
CREATE TABLE pages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    status ENUM('published', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Site ayarları
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog yazıları
CREATE TABLE blog_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(255),
    meta_title VARCHAR(255),
    meta_description TEXT,
    status ENUM('published', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Kategoriler
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Medya dosyaları
CREATE TABLE media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    alt_text VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```



### Dosya Yapısı
```
📁 project-root/
├── 📁 config/
│   ├── database.php (DB bağlantı ayarları)
│   └── app.php (genel ayarlar)
├── 📁 core/
│   ├── Database.php (PDO wrapper + multi-tenant)
│   ├── Router.php (URL routing sistemi)
│   ├── Middleware.php (domain tespiti)
│   ├── Language.php (çoklu dil sistemi)
│   ├── Theme.php (tema yönetimi)
│   └── Auth.php (kimlik doğrulama)
├── 📁 themes/
│   ├── 📁 lawyer/
│   │   ├── 📁 views/ (HTML şablonları)
│   │   ├── 📁 assets/ (CSS, JS, resimler)
│   │   └── theme.php (tema ayarları)
│   ├── 📁 dietitian/
│   │   ├── 📁 views/
│   │   ├── 📁 assets/
│   │   └── theme.php
│   └── 📁 technical/
│       ├── 📁 views/
│       ├── 📁 assets/
│       └── theme.php
├── 📁 admin/
│   ├── 📁 central/ (merkezi yönetim paneli)
│   │   ├── index.php
│   │   ├── tenants.php
│   │   └── themes.php
│   └── 📁 tenant/ (tenant admin paneli)
│       ├── index.php
│       ├── pages.php
│       ├── blog.php
│       └── settings.php
├── 📁 migrations/ (DB şema dosyaları)
│   ├── central_db.sql
│   └── tenant_db.sql
├── 📁 public/
│   ├── index.php (ana giriş noktası)
│   ├── .htaccess (URL rewriting)
│   └── 📁 assets/ (ortak dosyalar)
└── 📁 vendor/ (composer bağımlılıkları)
```

### Middleware Sistemi (Domain Tespiti ve DB Bağlantısı)

#### İşleyiş Akışı
```
1. İstek gelir (örn: avukat.com/hakkimizda)
2. Middleware domain'i tespit eder
3. Merkezi DB'den tenant ve tema bilgilerini alır
4. Tenant DB'sine bağlantı kurar (domain_com formatında)
5. Tema config dosyasını yükler
6. İsteği işler
```

#### Middleware Sınıfı Yapısı
```php
class TenantMiddleware {
    private $centralDB;
    private $tenantDB;
    private $currentTenant;
    private $themeConfig;
    
    public function handle($request) {
        // Domain tespiti
        $domain = $this->extractDomain($request);
        
        // Merkezi DB'den tenant bilgisi
        $tenant = $this->getTenantInfo($domain);
        
        // Tenant DB bağlantısı (avukat.com -> avukat_com)
        $dbName = str_replace('.', '_', $domain);
        $this->connectToTenantDB($dbName);
        
        // Tema config yükleme
        $this->loadThemeConfig($tenant['theme_id']);
    }
    
    private function loadThemeConfig($themeId) {
        $themeName = $this->getThemeName($themeId);
        $this->themeConfig = include "themes/{$themeName}/config.php";
    }
}
```

### Dinamik Tema Sistemi

#### Tema Yapısı
```
📁 themes/
├── 📁 lawyer1/
│   ├── 📁 views/
│   │   ├── layout.php (ana şablon)
│   │   ├── home.php (anasayfa)
│   │   ├── lawyers.php (avukat listesi)
│   │   └── lawyer-detail.php (avukat detay)
│   ├── 📁 assets/
│   │   ├── 📁 css/
│   │   ├── 📁 js/
│   │   └── 📁 images/
│   └── config.php (tema konfigürasyonu)
├── 📁 lawyer2/
│   ├── 📁 views/
│   │   ├── layout.php
│   │   ├── home.php
│   │   └── about.php (tek avukat için)
│   ├── 📁 assets/
│   └── config.php
└── 📁 dietitian1/
    ├── 📁 views/
    ├── 📁 assets/
    └── config.php
```

#### Tema Konfigürasyon Dosyası
```php
// themes/lawyer1/config.php
return [
    'name' => 'Çoklu Avukat Teması',
    'description' => 'Birden fazla avukat için liste ve detay sayfaları',
    'admin_modules' => [
        'lawyers' => [
            'title' => 'Avukatlar',
            'table' => 'lawyers',
            'multilingual' => true,
            'fields' => [
                'name' => [
                    'type' => 'text',
                    'label' => 'Avukat Adı',
                    'translatable' => true,
                    'required' => true
                ],
                'title' => [
                    'type' => 'text',
                    'label' => 'Unvan',
                    'translatable' => true
                ],
                'bio' => [
                    'type' => 'textarea',
                    'label' => 'Biyografi',
                    'translatable' => true
                ],
                'image' => [
                    'type' => 'file',
                    'label' => 'Fotoğraf',
                    'translatable' => false
                ],
                'email' => [
                    'type' => 'email',
                    'label' => 'E-posta',
                    'translatable' => false
                ],
                'specialization' => [
                    'type' => 'text',
                    'label' => 'Uzmanlık Alanı',
                    'translatable' => true
                ]
            ]
        ],
        'practice_areas' => [
            'title' => 'Uzmanlık Alanları',
            'table' => 'practice_areas',
            'multilingual' => true,
            'fields' => [
                'name' => [
                    'type' => 'text',
                    'label' => 'Alan Adı',
                    'translatable' => true,
                    'required' => true
                ],
                'description' => [
                    'type' => 'textarea',
                    'label' => 'Açıklama',
                    'translatable' => true
                ],
                'icon' => [
                    'type' => 'text',
                    'label' => 'İkon Sınıfı',
                    'translatable' => false
                ]
            ]
        ]
    ]
];

// themes/lawyer2/config.php
return [
    'name' => 'Bireysel Avukat Teması',
    'description' => 'Tek avukat için kişisel site',
    'admin_modules' => [
        'about' => [
            'title' => 'Hakkımızda',
            'table' => 'about_info',
            'multilingual' => true,
            'single_record' => true, // Tek kayıt
            'fields' => [
                'title' => [
                    'type' => 'text',
                    'label' => 'Başlık',
                    'translatable' => true
                ],
                'content' => [
                    'type' => 'editor',
                    'label' => 'İçerik',
                    'translatable' => true
                ],
                'image' => [
                    'type' => 'file',
                    'label' => 'Fotoğraf',
                    'translatable' => false
                ]
            ]
        ]
    ]
];
```

### Dinamik Çoklu Dil Sistemi

#### Dil Yönetimi
```php
class LanguageManager {
    public function getSystemLanguages() {
        return [
            'tr' => 'Türkçe',
            'en' => 'English',
            'de' => 'Deutsch',
            'fr' => 'Français',
            'ar' => 'العربية'
        ];
    }
    
    public function getActiveLanguages() {
        // Tenant'ın aktif ettiği diller
        return $this->db->query("SELECT * FROM languages WHERE is_active = 1 ORDER BY is_default DESC");
    }
    
    public function activateLanguage($code, $name, $isDefault = false) {
        if ($isDefault) {
            // Önceki varsayılan dili kaldır
            $this->db->update('languages', ['is_default' => 0], []);
        }
        
        $this->db->insert('languages', [
            'code' => $code,
            'name' => $name,
            'is_default' => $isDefault,
            'is_active' => true
        ]);
    }
}
```

#### Dinamik Admin Panel Generator
```php
class DynamicAdminPanel {
    private $themeConfig;
    private $activeLanguages;
    
    public function __construct($themeConfig) {
        $this->themeConfig = $themeConfig;
        $this->activeLanguages = (new LanguageManager())->getActiveLanguages();
    }
    
    public function generateMenu() {
        $menu = [];
        
        // Ortak menüler
        $menu[] = ['title' => 'Dashboard', 'url' => '/admin', 'icon' => 'dashboard'];
        $menu[] = ['title' => 'Sayfalar', 'url' => '/admin/pages', 'icon' => 'pages'];
        $menu[] = ['title' => 'Blog', 'url' => '/admin/blog', 'icon' => 'blog'];
        
        // Tema özel menüler
        foreach ($this->themeConfig['admin_modules'] as $key => $module) {
            $menu[] = [
                'title' => $module['title'],
                'url' => '/admin/' . $key,
                'icon' => $key
            ];
        }
        
        // Sistem menüleri
        $menu[] = ['title' => 'Dil Ayarları', 'url' => '/admin/languages', 'icon' => 'language'];
        $menu[] = ['title' => 'Site Ayarları', 'url' => '/admin/settings', 'icon' => 'settings'];
        $menu[] = ['title' => 'Medya', 'url' => '/admin/media', 'icon' => 'media'];
        
        return $menu;
    }
    
    public function generateForm($moduleKey, $data = null) {
        $module = $this->themeConfig['admin_modules'][$moduleKey];
        $html = '';
        
        if ($module['multilingual'] && count($this->activeLanguages) > 1) {
            $html .= $this->generateMultilingualForm($module, $data);
        } else {
            $html .= $this->generateSingleLanguageForm($module, $data);
        }
        
        return $html;
    }
    
    private function generateMultilingualForm($module, $data) {
        $html = '<div class="multilingual-form">';
        
        // Dil tabları
        $html .= '<div class="language-tabs">';
        foreach ($this->activeLanguages as $index => $lang) {
            $active = $index === 0 ? 'active' : '';
            $default = $lang['is_default'] ? ' (Varsayılan)' : '';
            $html .= "<button type='button' class='lang-tab {$active}' data-lang='{$lang['code']}'>";
            $html .= $lang['name'] . $default;
            $html .= "</button>";
        }
        $html .= '</div>';
        
        // Dil içerikleri
        foreach ($this->activeLanguages as $index => $lang) {
            $display = $index === 0 ? 'block' : 'none';
            $html .= "<div class='lang-content' data-lang='{$lang['code']}' style='display:{$display}'>";
            
            foreach ($module['fields'] as $fieldName => $fieldConfig) {
                if ($fieldConfig['translatable']) {
                    $value = $data[$fieldName][$lang['code']] ?? '';
                    $html .= $this->generateField($fieldName . '[' . $lang['code'] . ']', $fieldConfig, $value);
                }
            }
            $html .= '</div>';
        }
        
        // Dil bağımsız alanlar
        $html .= '<div class="non-translatable-fields">';
        foreach ($module['fields'] as $fieldName => $fieldConfig) {
            if (!$fieldConfig['translatable']) {
                $value = $data[$fieldName] ?? '';
                $html .= $this->generateField($fieldName, $fieldConfig, $value);
            }
        }
        $html .= '</div>';
        
        $html .= '</div>';
        return $html;
    }
    
    private function generateField($name, $config, $value = '') {
        $required = $config['required'] ?? false ? 'required' : '';
        $html = "<div class='form-group'>";
        $html .= "<label for='{$name}'>{$config['label']}</label>";
        
        switch ($config['type']) {
            case 'text':
                $html .= "<input type='text' name='{$name}' value='{$value}' {$required}>";
                break;
            case 'email':
                $html .= "<input type='email' name='{$name}' value='{$value}' {$required}>";
                break;
            case 'textarea':
                $html .= "<textarea name='{$name}' {$required}>{$value}</textarea>";
                break;
            case 'editor':
                $html .= "<textarea name='{$name}' class='editor' {$required}>{$value}</textarea>";
                break;
            case 'file':
                $html .= "<input type='file' name='{$name}'>";
                if ($value) {
                    $html .= "<div class='current-file'><img src='{$value}' style='max-width:100px'></div>";
                }
                break;
            case 'select':
                $html .= "<select name='{$name}' {$required}>";
                foreach ($config['options'] as $optValue => $optLabel) {
                    $selected = $value == $optValue ? 'selected' : '';
                    $html .= "<option value='{$optValue}' {$selected}>{$optLabel}</option>";
                }
                $html .= "</select>";
                break;
        }
        
        $html .= "</div>";
        return $html;
    }
}
```

### Migration Sistemi

#### Migration Dosya Yapısı
```
📁 migrations/
├── core.sql (temel tablolar - tüm tenant'larda)
├── lawyer1.sql (lawyer1 teması için özel tablolar)
├── lawyer2.sql (lawyer2 teması için özel tablolar)
└── dietitian1.sql (dietitian1 teması için özel tablolar)
```

#### Core Migration (core.sql)
```sql
-- Her tenant DB'sinde olması gereken temel tablolar
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE languages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(5) NOT NULL,
    name VARCHAR(50) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE translations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    language_id INT,
    translation_key VARCHAR(255) NOT NULL,
    translation_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (language_id) REFERENCES languages(id),
    UNIQUE KEY unique_lang_key (language_id, translation_key)
);

CREATE TABLE pages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    language_id INT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content LONGTEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    status ENUM('published', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (language_id) REFERENCES languages(id),
    UNIQUE KEY unique_lang_slug (language_id, slug)
);

CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE blog_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    language_id INT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(255),
    meta_title VARCHAR(255),
    meta_description TEXT,
    status ENUM('published', 'draft') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (language_id) REFERENCES languages(id),
    UNIQUE KEY unique_lang_slug (language_id, slug)
);

CREATE TABLE media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    alt_text VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Tema Özel Migration (lawyer1.sql)
```sql
-- Lawyer1 teması için özel tablolar
CREATE TABLE lawyers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    image VARCHAR(255),
    email VARCHAR(100),
    phone VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE lawyer_translations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lawyer_id INT,
    language_id INT,
    name VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    bio TEXT,
    specialization VARCHAR(255),
    FOREIGN KEY (lawyer_id) REFERENCES lawyers(id) ON DELETE CASCADE,
    FOREIGN KEY (language_id) REFERENCES languages(id),
    UNIQUE KEY unique_lawyer_lang (lawyer_id, language_id)
);

CREATE TABLE practice_areas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    icon VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE practice_area_translations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    practice_area_id INT,
    language_id INT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    FOREIGN KEY (practice_area_id) REFERENCES practice_areas(id) ON DELETE CASCADE,
    FOREIGN KEY (language_id) REFERENCES languages(id),
    UNIQUE KEY unique_area_lang (practice_area_id, language_id)
);
```

### URL Routing ve Çoklu Dil

#### Route Yapısı
```php
// Varsayılan dil
GET /                    -> HomeController@index
GET /avukatlar          -> LawyerController@index (tema varsa)
GET /avukatlar/{slug}   -> LawyerController@show (tema varsa)
GET /hakkimizda         -> PageController@show
GET /blog               -> BlogController@index
GET /blog/{slug}        -> BlogController@show

// Çoklu dil desteği
GET /{page}?lang=en     -> PageController@show (dil parametresi ile)
GET /lawyers?lang=en    -> LawyerController@index (İngilizce)

// Admin
GET /admin              -> AdminController@dashboard
GET /admin/lawyers      -> AdminLawyerController@index (tema varsa)
POST /admin/lawyers     -> AdminLawyerController@store (tema varsa)
```

#### Dinamik Route Generator
```php
class DynamicRouter {
    private $themeConfig;
    
    public function generateRoutes() {
        $routes = [];
        
        // Ortak route'lar
        $routes['GET']['/'] = 'HomeController@index';
        $routes['GET']['/blog'] = 'BlogController@index';
        $routes['GET']['/blog/{slug}'] = 'BlogController@show';
        
        // Tema özel route'lar
        foreach ($this->themeConfig['admin_modules'] as $key => $module) {
            if (!$module['single_record']) {
                $routes['GET']["/{$key}"] = ucfirst($key) . 'Controller@index';
                $routes['GET']["/{$key}/{slug}"] = ucfirst($key) . 'Controller@show';
            }
        }
        
        return $routes;
    }
}
```

### Güvenlik ve Authentication

#### Güvenlik Önlemleri
- CSRF token kontrolü (her form için)
- SQL Injection koruması (PDO prepared statements)
- XSS koruması (htmlspecialchars, strip_tags)
- File upload güvenliği (mime type, extension kontrolü)
- Session hijacking koruması
- Rate limiting (brute force koruması)

#### Authentication Sistemi
```php
class Auth {
    public function loginSuperAdmin($username, $password) {
        // Merkezi DB kontrolü
        $user = $this->centralDB->query(
            "SELECT * FROM super_admin_users WHERE username = ?", 
            [$username]
        )->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $_SESSION['super_admin'] = $user['id'];
            return true;
        }
        return false;
    }
    
    public function loginTenantAdmin($username, $password) {
        // Tenant DB kontrolü
        $user = $this->tenantDB->query(
            "SELECT * FROM admin_users WHERE username = ?", 
            [$username]
        )->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            $_SESSION['tenant_admin'] = $user['id'];
            $_SESSION['tenant_domain'] = $this->currentDomain;
            return true;
        }
        return false;
    }
}
```

### Deployment ve Kurulum

#### Tenant Oluşturma Süreci
```php
class TenantCreator {
    public function createTenant($domain, $themeId, $expiredAt) {
        // 1. Merkezi DB'ye tenant kaydı
        $this->centralDB->insert('tenants', [
            'domain' => $domain,
            'theme_id' => $themeId,
            'status' => 'active',
            'expired_at' => $expiredAt
        ]);
        
        // 2. Tenant DB oluştur
        $dbName = str_replace('.', '_', $domain);
        $this->createTenantDatabase($dbName);
        
        // 3. Core migration çalıştır
        $this->runMigration($dbName, 'core.sql');
        
        // 4. Tema özel migration çalıştır
        $themeName = $this->getThemeName($themeId);
        $this->runMigration($dbName, $themeName . '.sql');
        
        // 5. Varsayılan dil ekle
        $this->addDefaultLanguage($dbName);
        
        // 6. İlk admin kullanıcısı oluştur
        $this->createFirstAdmin($dbName);
    }
    
    private function runMigration($dbName, $migrationFile) {
        $sql = file_get_contents("migrations/{$migrationFile}");
        $this->executeSQL($dbName, $sql);
    }
}
