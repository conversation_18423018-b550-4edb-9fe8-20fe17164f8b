<?php

/**
 * Lawyer1 theme configuration
 * Multi-lawyer law firm theme with lawyer listings and practice areas
 * 
 * <AUTHOR> System
 * @version 1.0
 */

return [
    'name' => 'Çoklu Avukat Teması',
    'description' => 'Birden fazla avukat için liste ve detay sayfaları içeren hukuk bürosu teması',
    'version' => '1.0.0',
    'author' => 'Multi-Tenant System',
    
    // Admin modules configuration
    'admin_modules' => [
        'lawyers' => [
            'title' => 'Avukatlar',
            'table' => 'lawyers',
            'multilingual' => false,
            'icon' => 'fas fa-user-tie',
            'fields' => [
                'name' => [
                    'type' => 'text',
                    'label' => 'Avukat Adı',
                    'translatable' => false,
                    'required' => true,
                    'show_in_list' => true
                ],
                'title' => [
                    'type' => 'text',
                    'label' => 'Unvan',
                    'translatable' => false,
                    'show_in_list' => true
                ],
                'bio' => [
                    'type' => 'editor',
                    'label' => 'Biyografi',
                    'translatable' => false,
                    'show_in_list' => false
                ],
                'specialization' => [
                    'type' => 'text',
                    'label' => 'Uzmanlık Alanı',
                    'translatable' => false,
                    'show_in_list' => true
                ],
                'image' => [
                    'type' => 'file',
                    'label' => 'Fotoğraf',
                    'translatable' => false,
                    'accept' => 'image/*',
                    'show_in_list' => true
                ],
                'email' => [
                    'type' => 'email',
                    'label' => 'E-posta',
                    'translatable' => false,
                    'show_in_list' => true
                ],
                'phone' => [
                    'type' => 'text',
                    'label' => 'Telefon',
                    'translatable' => false,
                    'show_in_list' => true
                ],
                'status' => [
                    'type' => 'select',
                    'label' => 'Durum',
                    'translatable' => false,
                    'options' => [
                        'active' => 'Aktif',
                        'inactive' => 'Pasif'
                    ],
                    'show_in_list' => true
                ],
                'sort_order' => [
                    'type' => 'number',
                    'label' => 'Sıralama',
                    'translatable' => false,
                    'show_in_list' => false
                ]
            ]
        ],
        
        'practice_areas' => [
            'title' => 'Uzmanlık Alanları',
            'table' => 'practice_areas',
            'multilingual' => false,
            'icon' => 'fas fa-balance-scale',
            'fields' => [
                'name' => [
                    'type' => 'text',
                    'label' => 'Alan Adı',
                    'translatable' => false,
                    'required' => true,
                    'show_in_list' => true
                ],
                'description' => [
                    'type' => 'editor',
                    'label' => 'Açıklama',
                    'translatable' => false,
                    'show_in_list' => false
                ],
                'icon' => [
                    'type' => 'text',
                    'label' => 'İkon Sınıfı',
                    'translatable' => false,
                    'help' => 'FontAwesome icon sınıfı (örn: fas fa-gavel)',
                    'show_in_list' => true
                ],
                'status' => [
                    'type' => 'select',
                    'label' => 'Durum',
                    'translatable' => false,
                    'options' => [
                        'active' => 'Aktif',
                        'inactive' => 'Pasif'
                    ],
                    'show_in_list' => true
                ],
                'sort_order' => [
                    'type' => 'number',
                    'label' => 'Sıralama',
                    'translatable' => false,
                    'show_in_list' => false
                ]
            ]
        ],
        
        'services' => [
            'title' => 'Hizmetler',
            'table' => 'services',
            'multilingual' => false,
            'icon' => 'fas fa-handshake',
            'fields' => [
                'name' => [
                    'type' => 'text',
                    'label' => 'Hizmet Adı',
                    'translatable' => false,
                    'required' => true,
                    'show_in_list' => true
                ],
                'description' => [
                    'type' => 'editor',
                    'label' => 'Açıklama',
                    'translatable' => false,
                    'show_in_list' => false
                ],
                'icon' => [
                    'type' => 'text',
                    'label' => 'İkon Sınıfı',
                    'translatable' => false,
                    'help' => 'FontAwesome icon sınıfı (örn: fas fa-file-contract)',
                    'show_in_list' => true
                ],
                'status' => [
                    'type' => 'select',
                    'label' => 'Durum',
                    'translatable' => false,
                    'options' => [
                        'active' => 'Aktif',
                        'inactive' => 'Pasif'
                    ],
                    'show_in_list' => true
                ],
                'sort_order' => [
                    'type' => 'number',
                    'label' => 'Sıralama',
                    'translatable' => false,
                    'show_in_list' => false
                ]
            ]
        ],
        
        'testimonials' => [
            'title' => 'Müvekkil Yorumları',
            'table' => 'testimonials',
            'multilingual' => false,
            'icon' => 'fas fa-quote-left',
            'fields' => [
                'client_name' => [
                    'type' => 'text',
                    'label' => 'Müvekkil Adı',
                    'translatable' => false,
                    'required' => true,
                    'show_in_list' => true
                ],
                'client_title' => [
                    'type' => 'text',
                    'label' => 'Müvekkil Unvanı',
                    'translatable' => false,
                    'show_in_list' => true
                ],
                'testimonial_text' => [
                    'type' => 'textarea',
                    'label' => 'Yorum Metni',
                    'translatable' => false,
                    'required' => true,
                    'show_in_list' => true
                ],
                'client_image' => [
                    'type' => 'file',
                    'label' => 'Müvekkil Fotoğrafı',
                    'translatable' => false,
                    'accept' => 'image/*',
                    'show_in_list' => true
                ],
                'rating' => [
                    'type' => 'select',
                    'label' => 'Puan',
                    'translatable' => false,
                    'options' => [
                        '5' => '5 Yıldız',
                        '4' => '4 Yıldız',
                        '3' => '3 Yıldız',
                        '2' => '2 Yıldız',
                        '1' => '1 Yıldız'
                    ],
                    'show_in_list' => true
                ],
                'status' => [
                    'type' => 'select',
                    'label' => 'Durum',
                    'translatable' => false,
                    'options' => [
                        'active' => 'Aktif',
                        'inactive' => 'Pasif'
                    ],
                    'show_in_list' => true
                ],
                'sort_order' => [
                    'type' => 'number',
                    'label' => 'Sıralama',
                    'translatable' => false,
                    'show_in_list' => false
                ]
            ]
        ]
    ],
    
    // Theme settings
    'settings' => [
        'primary_color' => '#1a365d',
        'secondary_color' => '#2d3748',
        'accent_color' => '#3182ce',
        'font_family' => 'Inter, sans-serif',
        'layout' => 'full-width'
    ],
    
    // Frontend routes
    'routes' => [
        'lawyers' => [
            'list' => '/avukatlar',
            'detail' => '/avukatlar/{slug}'
        ],
        'practice_areas' => [
            'list' => '/uzmanlik-alanlari',
            'detail' => '/uzmanlik-alanlari/{slug}'
        ],
        'services' => [
            'list' => '/hizmetler',
            'detail' => '/hizmetler/{slug}'
        ]
    ],
    
    // Menu structure for frontend
    'menu_structure' => [
        'main_menu' => [
            'home' => 'Anasayfa',
            'lawyers' => 'Avukatlarımız',
            'practice_areas' => 'Uzmanlık Alanları',
            'services' => 'Hizmetlerimiz',
            'blog' => 'Blog',
            'contact' => 'İletişim'
        ]
    ]
];