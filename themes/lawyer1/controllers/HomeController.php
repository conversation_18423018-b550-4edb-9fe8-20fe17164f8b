<?php

/**
 * Home Controller for Lawyer Theme
 * Handles homepage display for tenant sites
 */
class HomeController
{
    private $db;
    private $themeConfig;

    public function __construct()
    {
        // Get database connection from middleware
        global $middleware;
        if ($middleware) {
            $this->db = $middleware->getDatabase();
            $this->themeConfig = $middleware->getThemeConfig();
        } else {
            throw new Exception("Middleware not initialized");
        }
    }

    /**
     * Display homepage
     */
    public function index()
    {
        try {
            // Get homepage content from database
            $stmt = $this->db->prepare("SELECT * FROM pages WHERE slug = 'homepage' AND status = 'active' LIMIT 1");
            $stmt->execute();
            $homepage = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get latest blog posts for homepage
            $stmt = $this->db->prepare("SELECT * FROM blog_posts WHERE status = 'published' ORDER BY created_at DESC LIMIT 3");
            $stmt->execute();
            $latestPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get services for lawyer theme
            $stmt = $this->db->prepare("SELECT * FROM services WHERE status = 'active' ORDER BY sort_order ASC LIMIT 6");
            $stmt->execute();
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get testimonials
            $stmt = $this->db->prepare("SELECT * FROM testimonials WHERE status = 'active' ORDER BY created_at DESC LIMIT 5");
            $stmt->execute();
            $testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get site settings
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM settings");
            $stmt->execute();
            $settingsData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $settings = [];
            foreach ($settingsData as $setting) {
                $settings[$setting['setting_key']] = $setting['setting_value'];
            }

            // Prepare data for view
            $data = [
                'homepage' => $homepage,
                'latestPosts' => $latestPosts,
                'services' => $services,
                'testimonials' => $testimonials,
                'settings' => $settings,
                'themeConfig' => $this->themeConfig
            ];

            // Load homepage view
            $this->loadView('home', $data);

        } catch (Exception $e) {
            // Error handling - show basic homepage
            $data = [
                'error' => 'Sayfa yüklenirken bir hata oluştu.',
                'themeConfig' => $this->themeConfig
            ];
            $this->loadView('error', $data);
        }
    }

    /**
     * Load view file with data
     */
    private function loadView($viewName, $data = [])
    {
        // Extract data to variables
        extract($data);
        
        // Get theme name
        $themeName = defined('THEME_NAME') ? THEME_NAME : 'lawyer1';
        
        // View file path
        $viewPath = __DIR__ . "/../views/{$viewName}.php";
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Fallback view
            echo "<h1>Hoş Geldiniz</h1>";
            echo "<p>Ana sayfa içeriği yükleniyor...</p>";
            if (isset($error)) {
                echo "<div class='alert alert-danger'>{$error}</div>";
            }
        }
    }
}