<?php

/**
 * Services Controller for Lawyer Theme
 * Handles services listing and detail pages
 */
class ServicesController
{
    private $db;
    private $themeConfig;

    public function __construct()
    {
        // Get database connection from middleware
        global $middleware;
        if ($middleware) {
            $this->db = $middleware->getDatabase();
            $this->themeConfig = $middleware->getThemeConfig();
        } else {
            throw new Exception("Middleware not initialized");
        }
    }

    /**
     * Display services listing page
     */
    public function index()
    {
        try {
            // Get all active services
            $stmt = $this->db->prepare("SELECT * FROM services WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
            $stmt->execute();
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get site settings
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM settings");
            $stmt->execute();
            $settingsData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $settings = [];
            foreach ($settingsData as $setting) {
                $settings[$setting['setting_key']] = $setting['setting_value'];
            }

            // Prepare data for view
            $data = [
                'services' => $services,
                'settings' => $settings,
                'themeConfig' => $this->themeConfig,
                'pageTitle' => 'Hizmetlerimiz'
            ];

            // Load services listing view
            $this->loadView('services', $data);

        } catch (Exception $e) {
            // Error handling
            $data = [
                'error' => 'Hizmetler yüklenirken bir hata oluştu: ' . $e->getMessage(),
                'themeConfig' => $this->themeConfig
            ];
            $this->loadView('error', $data);
        }
    }

    /**
     * Display single service detail page
     */
    public function show($slug)
    {
        try {
            // Since we don't have slug column, we'll use id or name
            // For now, let's assume slug is the service id
            $stmt = $this->db->prepare("SELECT * FROM services WHERE id = ? AND status = 'active' LIMIT 1");
            $stmt->execute([$slug]);
            $service = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$service) {
                throw new Exception("Service not found", 404);
            }

            // Get other services as related services
            $stmt = $this->db->prepare("SELECT * FROM services WHERE id != ? AND status = 'active' ORDER BY sort_order ASC LIMIT 3");
            $stmt->execute([$slug]);
            $relatedServices = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get site settings
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM settings");
            $stmt->execute();
            $settingsData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $settings = [];
            foreach ($settingsData as $setting) {
                $settings[$setting['setting_key']] = $setting['setting_value'];
            }

            // Prepare data for view
            $data = [
                'service' => $service,
                'relatedServices' => $relatedServices,
                'settings' => $settings,
                'themeConfig' => $this->themeConfig,
                'pageTitle' => $service['name']
            ];

            // Load service detail view
            $this->loadView('service-detail', $data);

        } catch (Exception $e) {
            if ($e->getCode() == 404) {
                http_response_code(404);
                $data = [
                    'error' => 'Aradığınız hizmet bulunamadı.',
                    'themeConfig' => $this->themeConfig
                ];
            } else {
                $data = [
                    'error' => 'Hizmet detayları yüklenirken bir hata oluştu: ' . $e->getMessage(),
                    'themeConfig' => $this->themeConfig
                ];
            }
            $this->loadView('error', $data);
        }
    }

    /**
     * Display services by category (not available with current DB structure)
     */
    public function category($categorySlug)
    {
        // Since we don't have category column, redirect to main services page
        header('Location: /services');
        exit;
    }

    /**
     * Load view file with data
     */
    private function loadView($viewName, $data = [])
    {
        // Extract data to variables
        extract($data);
        
        // Get theme name
        $themeName = defined('THEME_NAME') ? THEME_NAME : 'lawyer1';
        
        // View file path
        $viewPath = __DIR__ . "/../views/{$viewName}.php";
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            // Fallback view
            echo "<h1>Sayfa Bulunamadı</h1>";
            echo "<p>İstenen view dosyası bulunamadı: {$viewName}.php</p>";
            if (isset($error)) {
                echo "<div class='alert alert-danger'>{$error}</div>";
            }
        }
    }
}