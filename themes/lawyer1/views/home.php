<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 100px 0;
        }
        .service-card {
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .testimonial-card {
            background: #f8f9fa;
            border-left: 4px solid #2a5298;
        }
        .footer {
            background: #1e3c72;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-balance-scale me-2"></i>
                <?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Ana Sayfa</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/services">Hizmetlerimiz</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">İletişim</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        <?= isset($homepage['title']) ? $homepage['title'] : 'Hukuki Danışmanlık Hizmetleri' ?>
                    </h1>
                    <p class="lead mb-4">
                        <?= isset($homepage['content']) ? strip_tags($homepage['content']) : 'Profesyonel avukatlık hizmetleri ile yanınızdayız. Hukuki sorunlarınız için güvenilir çözümler sunuyoruz.' ?>
                    </p>
                    <a href="/contact" class="btn btn-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Hemen İletişime Geçin
                    </a>
                </div>
                <div class="col-lg-6">
                    <img src="https://via.placeholder.com/600x400/2a5298/ffffff?text=Avukat+Ofisi" 
                         class="img-fluid rounded shadow" alt="Avukat Ofisi">
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <?php if (!empty($services)): ?>
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Hizmetlerimiz</h2>
                    <p class="lead text-muted">Geniş hukuki hizmet yelpazesi ile yanınızdayız</p>
                </div>
            </div>
            <div class="row">
                <?php foreach ($services as $service): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card service-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-gavel fa-3x text-primary mb-3"></i>
                            <h5 class="card-title"><?= htmlspecialchars($service['name']) ?></h5>
                            <p class="card-text"><?= htmlspecialchars(substr($service['description'], 0, 100)) ?>...</p>
                            <a href="/services/<?= $service['id'] ?>" class="btn btn-outline-primary">Detaylar</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Latest Blog Posts -->
    <?php if (!empty($latestPosts)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Son Yazılar</h2>
                    <p class="lead text-muted">Hukuk dünyasından güncel haberler ve makaleler</p>
                </div>
            </div>
            <div class="row">
                <?php foreach ($latestPosts as $post): ?>
                <div class="col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title"><?= htmlspecialchars($post['title']) ?></h5>
                            <p class="card-text"><?= htmlspecialchars(substr(strip_tags($post['content']), 0, 150)) ?>...</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= date('d.m.Y', strtotime($post['created_at'])) ?>
                            </small>
                        </div>
                        <div class="card-footer">
                            <a href="/blog/<?= $post['slug'] ?>" class="btn btn-primary btn-sm">Devamını Oku</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Testimonials -->
    <?php if (!empty($testimonials)): ?>
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Müvekkil Yorumları</h2>
                    <p class="lead text-muted">Memnun müvekkillerimizin görüşleri</p>
                </div>
            </div>
            <div class="row">
                <?php foreach (array_slice($testimonials, 0, 3) as $testimonial): ?>
                <div class="col-lg-4 mb-4">
                    <div class="testimonial-card p-4 rounded">
                        <p class="mb-3">"<?= htmlspecialchars($testimonial['content']) ?>"</p>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-circle fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= htmlspecialchars($testimonial['client_name']) ?></h6>
                                <small class="text-muted"><?= htmlspecialchars($testimonial['client_title']) ?></small>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Contact CTA -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Hukuki Danışmanlık İhtiyacınız mı Var?</h2>
                    <p class="lead mb-4">Deneyimli avukat kadromuz ile hukuki sorunlarınıza çözüm buluyoruz.</p>
                    <a href="/contact" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-phone me-2"></i>Hemen Arayın
                    </a>
                    <a href="/contact" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Mesaj Gönderin
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?></h5>
                    <p><?= isset($settings['site_description']) ? $settings['site_description'] : 'Profesyonel hukuki danışmanlık hizmetleri' ?></p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5>İletişim</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i><?= isset($settings['address']) ? $settings['address'] : 'Adres bilgisi' ?></p>
                    <p><i class="fas fa-phone me-2"></i><?= isset($settings['phone']) ? $settings['phone'] : 'Telefon' ?></p>
                    <p><i class="fas fa-envelope me-2"></i><?= isset($settings['email']) ? $settings['email'] : 'E-posta' ?></p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="/services" class="text-light text-decoration-none">Hizmetlerimiz</a></li>
                        <li><a href="/about" class="text-light text-decoration-none">Hakkımızda</a></li>
                        <li><a href="/blog" class="text-light text-decoration-none">Blog</a></li>
                        <li><a href="/contact" class="text-light text-decoration-none">İletişim</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <p>&copy; <?= date('Y') ?> <?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?>. Tüm hakları saklıdır.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>