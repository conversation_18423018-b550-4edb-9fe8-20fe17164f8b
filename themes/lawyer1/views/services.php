<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle : 'Hizmetlerimiz' ?> - <?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 80px 0 60px;
        }
        .service-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            height: 100%;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
        .breadcrumb {
            background: transparent;
            padding: 0;
        }
        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255,255,255,0.7);
        }
        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: white;
        }
        .footer {
            background: #1e3c72;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-balance-scale me-2"></i>
                <?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Ana Sayfa</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/services">Hizmetlerimiz</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">İletişim</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Hizmetlerimiz</li>
                </ol>
            </nav>
            
            <div class="row">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        <?= isset($pageTitle) ? htmlspecialchars($pageTitle) : 'Hizmetlerimiz' ?>
                    </h1>
                    <p class="lead">
                        Geniş hukuki hizmet yelpazesi ile her türlü hukuki ihtiyacınıza çözüm sunuyoruz
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Grid -->
    <section class="py-5">
        <div class="container">
            <?php if (!empty($services)): ?>
                <div class="row">
                    <?php foreach ($services as $service): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card service-card">
                            <div class="card-body text-center p-4">
                                <div class="service-icon">
                                    <?php if (!empty($service['icon'])): ?>
                                        <i class="<?= htmlspecialchars($service['icon']) ?> fa-2x text-white"></i>
                                    <?php else: ?>
                                        <i class="fas fa-gavel fa-2x text-white"></i>
                                    <?php endif; ?>
                                </div>
                                
                                <h5 class="card-title mb-3"><?= htmlspecialchars($service['name']) ?></h5>
                                
                                <p class="card-text text-muted">
                                    <?= htmlspecialchars(substr(strip_tags($service['description']), 0, 120)) ?>...
                                </p>
                                
                                <a href="/services/<?= $service['id'] ?>" class="btn btn-primary">
                                    Detayları Gör <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h3 class="text-muted">Hizmet Bulunamadı</h3>
                            <p class="text-muted">Henüz hizmet eklenmemiştir.</p>
                            <a href="/" class="btn btn-primary">Ana Sayfaya Dön</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Hukuki Danışmanlık İhtiyacınız mı Var?</h2>
                    <p class="lead mb-4">Uzman avukat kadromuz ile size en uygun hizmet paketini belirleyelim.</p>
                    <a href="/contact" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-phone me-2"></i>Hemen Arayın
                    </a>
                    <a href="/contact" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Teklif Alın
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?></h5>
                    <p><?= isset($settings['site_description']) ? $settings['site_description'] : 'Profesyonel hukuki danışmanlık hizmetleri' ?></p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5>İletişim</h5>
                    <p><i class="fas fa-map-marker-alt me-2"></i><?= isset($settings['address']) ? $settings['address'] : 'Adres bilgisi' ?></p>
                    <p><i class="fas fa-phone me-2"></i><?= isset($settings['phone']) ? $settings['phone'] : 'Telefon' ?></p>
                    <p><i class="fas fa-envelope me-2"></i><?= isset($settings['email']) ? $settings['email'] : 'E-posta' ?></p>
                </div>
                <div class="col-lg-4 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="/services" class="text-light text-decoration-none">Hizmetlerimiz</a></li>
                        <li><a href="/about" class="text-light text-decoration-none">Hakkımızda</a></li>
                        <li><a href="/blog" class="text-light text-decoration-none">Blog</a></li>
                        <li><a href="/contact" class="text-light text-decoration-none">İletişim</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <p>&copy; <?= date('Y') ?> <?= isset($settings['site_title']) ? $settings['site_title'] : 'Avukat Ofisi' ?>. Tüm hakları saklıdır.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>