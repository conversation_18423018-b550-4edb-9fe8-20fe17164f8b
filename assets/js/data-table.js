$(document).ready(function() {
    $('#dataTableExample').DataTable({
        "columnDefs": [
            {
                "targets": "no-sort",
                "orderable": false
            }
        ],
        "order": [[3, "asc"]], // <PERSON> (4. s<PERSON><PERSON>, index 3) - az süre kalan üstte
        "language": {
            "lengthMenu": "_MENU_ kayıt göster",
            "zeroRecords": "Kayıt bulunamadı",
            "info": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
            "infoEmpty": "Kayıt yok",
            "infoFiltered": "(_MAX_ kayıt içinden filtrelendi)",
            "search": "Ara:",
            "paginate": {
                "first": "İlk",
                "last": "Son",
                "next": "Sonraki",
                "previous": "Ö<PERSON><PERSON>"
            }
        },
        "pageLength": 10,
        "responsive": true
    });
});