<?php

/**
 * Dynamic admin panel generator for multi-tenant themes
 * Generates admin interface based on theme configuration
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class DynamicAdminPanel
{
    private $themeConfig;
    private $activeLanguages;
    private $database;
    private $tenantDbName;

    public function __construct(array $themeConfig, string $tenantDbName)
    {
        $this->themeConfig = $themeConfig;
        $this->tenantDbName = $tenantDbName;
        $this->database = new Database();
        $this->activeLanguages = $this->loadActiveLanguages();
    }

    /**
     * Load active languages for current tenant (DISABLED - Single language only)
     * 
     * @return array
     */
    private function loadActiveLanguages(): array
    {
        // Multi-language support disabled - return empty array
        return [];
    }

    /**
     * Generate admin menu based on theme configuration
     * 
     * @return array
     */
    public function generateMenu(): array
    {
        $menu = [];
        
        // Common menu items
        $menu[] = [
            'title' => 'Dashboard',
            'url' => '/admin',
            'icon' => 'fas fa-tachometer-alt',
            'active' => $this->isCurrentPage('/admin')
        ];
        
        $menu[] = [
            'title' => 'Sayfalar',
            'url' => '/admin/pages',
            'icon' => 'fas fa-file-alt',
            'active' => $this->isCurrentPage('/admin/pages')
        ];
        
        $menu[] = [
            'title' => 'Blog',
            'url' => '/admin/blog',
            'icon' => 'fas fa-blog',
            'active' => $this->isCurrentPage('/admin/blog')
        ];
        
        // Theme-specific menu items
        if (isset($this->themeConfig['admin_modules'])) {
            foreach ($this->themeConfig['admin_modules'] as $moduleKey => $moduleConfig) {
                $menu[] = [
                    'title' => $moduleConfig['title'],
                    'url' => "/admin/{$moduleKey}",
                    'icon' => $moduleConfig['icon'] ?? 'fas fa-cog',
                    'active' => $this->isCurrentPage("/admin/{$moduleKey}")
                ];
            }
        }
        
        // System menu items
        // Language settings removed - multi-language support disabled
        
        $menu[] = [
            'title' => 'Site Ayarları',
            'url' => '/admin/settings',
            'icon' => 'fas fa-cogs',
            'active' => $this->isCurrentPage('/admin/settings')
        ];
        
        $menu[] = [
            'title' => 'Medya',
            'url' => '/admin/media',
            'icon' => 'fas fa-images',
            'active' => $this->isCurrentPage('/admin/media')
        ];
        
        return $menu;
    }

    /**
     * Check if current page matches given URL
     * 
     * @param string $url
     * @return bool
     */
    private function isCurrentPage(string $url): bool
    {
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
        $currentUrl = strtok($currentUrl, '?'); // Remove query string
        
        return $currentUrl === $url || strpos($currentUrl, $url . '/') === 0;
    }

    /**
     * Generate form for specific module
     * 
     * @param string $moduleKey
     * @param array|null $data
     * @param string $action
     * @return string
     */
    public function generateForm(string $moduleKey, ?array $data = null, string $action = 'create'): string
    {
        if (!isset($this->themeConfig['admin_modules'][$moduleKey])) {
            throw new Exception("Module not found: {$moduleKey}");
        }
        
        $module = $this->themeConfig['admin_modules'][$moduleKey];
        $html = '';
        
        // Form start
        $formAction = $action === 'create' ? "/admin/{$moduleKey}" : "/admin/{$moduleKey}/" . ($data['id'] ?? '');
        $html .= "<form method='POST' action='{$formAction}' enctype='multipart/form-data' class='admin-form'>";
        
        // CSRF token
        $auth = new Auth();
        $csrfToken = $auth->generateCsrfToken();
        $html .= "<input type='hidden' name='_token' value='{$csrfToken}'>";
        
        // Multi-language support disabled - always use single language form
        $html .= $this->generateSingleLanguageForm($module, $data);
        
        // Form buttons
        $html .= '<div class="form-actions">';
        $html .= '<button type="submit" class="btn btn-primary">';
        $html .= $action === 'create' ? 'Kaydet' : 'Güncelle';
        $html .= '</button>';
        $html .= '<a href="/admin/' . $moduleKey . '" class="btn btn-secondary">İptal</a>';
        $html .= '</div>';
        
        $html .= '</form>';
        
        return $html;
    }

    /**
     * Generate multilingual form
     * 
     * @param array $module
     * @param array|null $data
     * @return string
     */
    private function generateMultilingualForm(array $module, ?array $data): string
    {
        $html = '<div class="multilingual-form">';
        
        // Language tabs
        $html .= '<div class="language-tabs nav nav-tabs" role="tablist">';
        foreach ($this->activeLanguages as $index => $lang) {
            $active = $index === 0 ? 'active' : '';
            $default = $lang['is_default'] ? ' (Varsayılan)' : '';
            
            $html .= "<button class='nav-link {$active}' id='lang-{$lang['code']}-tab' ";
            $html .= "data-bs-toggle='tab' data-bs-target='#lang-{$lang['code']}' ";
            $html .= "type='button' role='tab'>";
            $html .= $lang['name'] . $default;
            $html .= "</button>";
        }
        $html .= '</div>';
        
        // Language content
        $html .= '<div class="tab-content">';
        foreach ($this->activeLanguages as $index => $lang) {
            $active = $index === 0 ? 'show active' : '';
            
            $html .= "<div class='tab-pane fade {$active}' id='lang-{$lang['code']}' ";
            $html .= "role='tabpanel' aria-labelledby='lang-{$lang['code']}-tab'>";
            
            // Translatable fields for this language
            foreach ($module['fields'] as $fieldName => $fieldConfig) {
                if ($fieldConfig['translatable']) {
                    $fieldValue = '';
                    if ($data && isset($data['translations'][$lang['code']][$fieldName])) {
                        $fieldValue = $data['translations'][$lang['code']][$fieldName];
                    }
                    
                    $html .= $this->generateField(
                        $fieldName . '[' . $lang['code'] . ']',
                        $fieldConfig,
                        $fieldValue,
                        $lang['name']
                    );
                }
            }
            
            $html .= '</div>';
        }
        $html .= '</div>';
        
        // Non-translatable fields
        $html .= '<div class="non-translatable-fields mt-4">';
        $html .= '<h5>Genel Bilgiler</h5>';
        foreach ($module['fields'] as $fieldName => $fieldConfig) {
            if (!$fieldConfig['translatable']) {
                $fieldValue = $data[$fieldName] ?? '';
                $html .= $this->generateField($fieldName, $fieldConfig, $fieldValue);
            }
        }
        $html .= '</div>';
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Generate single language form
     * 
     * @param array $module
     * @param array|null $data
     * @return string
     */
    private function generateSingleLanguageForm(array $module, ?array $data): string
    {
        $html = '<div class="single-language-form">';
        
        foreach ($module['fields'] as $fieldName => $fieldConfig) {
            $fieldValue = '';
            
            // Multi-language support disabled - get value directly
            $fieldValue = $data[$fieldName] ?? '';
            
            $html .= $this->generateField($fieldName, $fieldConfig, $fieldValue);
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Generate individual form field
     * 
     * @param string $name
     * @param array $config
     * @param string $value
     * @param string|null $langSuffix
     * @return string
     */
    private function generateField(string $name, array $config, string $value = '', ?string $langSuffix = null): string
    {
        $required = ($config['required'] ?? false) ? 'required' : '';
        $label = $config['label'] . ($langSuffix ? " ({$langSuffix})" : '');
        $fieldId = str_replace(['[', ']'], ['_', ''], $name);
        
        $html = "<div class='form-group mb-3'>";
        $html .= "<label for='{$fieldId}' class='form-label'>{$label}";
        if ($config['required'] ?? false) {
            $html .= " <span class='text-danger'>*</span>";
        }
        $html .= "</label>";
        
        switch ($config['type']) {
            case 'text':
                $html .= "<input type='text' class='form-control' id='{$fieldId}' name='{$name}' value='" . htmlspecialchars($value) . "' {$required}>";
                break;
                
            case 'email':
                $html .= "<input type='email' class='form-control' id='{$fieldId}' name='{$name}' value='" . htmlspecialchars($value) . "' {$required}>";
                break;
                
            case 'number':
                $html .= "<input type='number' class='form-control' id='{$fieldId}' name='{$name}' value='" . htmlspecialchars($value) . "' {$required}>";
                break;
                
            case 'textarea':
                $html .= "<textarea class='form-control' id='{$fieldId}' name='{$name}' rows='4' {$required}>" . htmlspecialchars($value) . "</textarea>";
                break;
                
            case 'editor':
                $html .= "<textarea class='form-control editor' id='{$fieldId}' name='{$name}' rows='8' {$required}>" . htmlspecialchars($value) . "</textarea>";
                break;
                
            case 'file':
                $html .= "<input type='file' class='form-control' id='{$fieldId}' name='{$name}' accept='" . ($config['accept'] ?? 'image/*') . "'>";
                if ($value) {
                    $html .= "<div class='current-file mt-2'>";
                    if (in_array(pathinfo($value, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif'])) {
                        $html .= "<img src='{$value}' alt='Current file' style='max-width: 200px; max-height: 150px;' class='img-thumbnail'>";
                    } else {
                        $html .= "<a href='{$value}' target='_blank' class='btn btn-sm btn-outline-primary'>Mevcut Dosyayı Görüntüle</a>";
                    }
                    $html .= "</div>";
                }
                break;
                
            case 'select':
                $html .= "<select class='form-control' id='{$fieldId}' name='{$name}' {$required}>";
                if (!($config['required'] ?? false)) {
                    $html .= "<option value=''>Seçiniz...</option>";
                }
                foreach ($config['options'] as $optValue => $optLabel) {
                    $selected = $value == $optValue ? 'selected' : '';
                    $html .= "<option value='" . htmlspecialchars($optValue) . "' {$selected}>" . htmlspecialchars($optLabel) . "</option>";
                }
                $html .= "</select>";
                break;
                
            case 'checkbox':
                $checked = $value ? 'checked' : '';
                $html .= "<div class='form-check'>";
                $html .= "<input type='checkbox' class='form-check-input' id='{$fieldId}' name='{$name}' value='1' {$checked}>";
                $html .= "<label class='form-check-label' for='{$fieldId}'>{$config['label']}</label>";
                $html .= "</div>";
                break;
                
            case 'date':
                $html .= "<input type='date' class='form-control' id='{$fieldId}' name='{$name}' value='" . htmlspecialchars($value) . "' {$required}>";
                break;
                
            case 'datetime':
                $html .= "<input type='datetime-local' class='form-control' id='{$fieldId}' name='{$name}' value='" . htmlspecialchars($value) . "' {$required}>";
                break;
        }
        
        if (isset($config['help'])) {
            $html .= "<small class='form-text text-muted'>{$config['help']}</small>";
        }
        
        $html .= "</div>";
        
        return $html;
    }

    /**
     * Generate data table for module listing
     * 
     * @param string $moduleKey
     * @param array $data
     * @param int $currentPage
     * @param int $totalPages
     * @return string
     */
    public function generateDataTable(string $moduleKey, array $data, int $currentPage = 1, int $totalPages = 1): string
    {
        if (!isset($this->themeConfig['admin_modules'][$moduleKey])) {
            throw new Exception("Module not found: {$moduleKey}");
        }
        
        $module = $this->themeConfig['admin_modules'][$moduleKey];
        $html = '';
        
        // Table header
        $html .= '<div class="table-responsive">';
        $html .= '<table class="table table-striped table-hover">';
        $html .= '<thead class="table-dark">';
        $html .= '<tr>';
        
        // Generate table headers based on fields
        foreach ($module['fields'] as $fieldName => $fieldConfig) {
            if ($fieldConfig['show_in_list'] ?? true) {
                $html .= "<th>{$fieldConfig['label']}</th>";
            }
        }
        
        $html .= '<th width="150">İşlemler</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        // Table rows
        if (empty($data)) {
            $colCount = count(array_filter($module['fields'], fn($f) => $f['show_in_list'] ?? true)) + 1;
            $html .= "<tr><td colspan='{$colCount}' class='text-center text-muted'>Kayıt bulunamadı</td></tr>";
        } else {
            foreach ($data as $row) {
                $html .= '<tr>';
                
                foreach ($module['fields'] as $fieldName => $fieldConfig) {
                    if ($fieldConfig['show_in_list'] ?? true) {
                        $value = '';
                        
                        // Multi-language support disabled - get value directly
                        $value = $row[$fieldName] ?? '';
                        
                        // Format value based on field type
                        $value = $this->formatTableValue($value, $fieldConfig);
                        $html .= "<td>{$value}</td>";
                    }
                }
                
                // Action buttons
                $html .= '<td>';
                $html .= "<a href='/admin/{$moduleKey}/{$row['id']}/edit' class='btn btn-sm btn-primary me-1'>Düzenle</a>";
                $html .= "<button type='button' class='btn btn-sm btn-danger' onclick='deleteRecord({$row['id']})'>Sil</button>";
                $html .= '</td>';
                
                $html .= '</tr>';
            }
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        
        // Pagination
        if ($totalPages > 1) {
            $html .= $this->generatePagination($currentPage, $totalPages, "/admin/{$moduleKey}");
        }
        
        return $html;
    }

    /**
     * Format table value based on field type
     * 
     * @param mixed $value
     * @param array $fieldConfig
     * @return string
     */
    private function formatTableValue($value, array $fieldConfig): string
    {
        switch ($fieldConfig['type']) {
            case 'file':
                if ($value) {
                    if (in_array(pathinfo($value, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif'])) {
                        return "<img src='{$value}' alt='Image' style='max-width: 50px; max-height: 50px;' class='img-thumbnail'>";
                    } else {
                        return "<a href='{$value}' target='_blank'>Dosyayı Görüntüle</a>";
                    }
                }
                return '-';
                
            case 'textarea':
            case 'editor':
                return strlen($value) > 100 ? substr(strip_tags($value), 0, 100) . '...' : strip_tags($value);
                
            case 'checkbox':
                return $value ? '<span class="badge bg-success">Evet</span>' : '<span class="badge bg-secondary">Hayır</span>';
                
            case 'date':
                return $value ? date('d.m.Y', strtotime($value)) : '-';
                
            case 'datetime':
                return $value ? date('d.m.Y H:i', strtotime($value)) : '-';
                
            default:
                return htmlspecialchars($value ?: '-');
        }
    }

    /**
     * Generate pagination HTML
     * 
     * @param int $currentPage
     * @param int $totalPages
     * @param string $baseUrl
     * @return string
     */
    private function generatePagination(int $currentPage, int $totalPages, string $baseUrl): string
    {
        $html = '<nav aria-label="Sayfa navigasyonu">';
        $html .= '<ul class="pagination justify-content-center">';
        
        // Previous button
        if ($currentPage > 1) {
            $html .= "<li class='page-item'><a class='page-link' href='{$baseUrl}?page=" . ($currentPage - 1) . "'>Önceki</a></li>";
        } else {
            $html .= "<li class='page-item disabled'><span class='page-link'>Önceki</span></li>";
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $currentPage) {
                $html .= "<li class='page-item active'><span class='page-link'>{$i}</span></li>";
            } else {
                $html .= "<li class='page-item'><a class='page-link' href='{$baseUrl}?page={$i}'>{$i}</a></li>";
            }
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $html .= "<li class='page-item'><a class='page-link' href='{$baseUrl}?page=" . ($currentPage + 1) . "'>Sonraki</a></li>";
        } else {
            $html .= "<li class='page-item disabled'><span class='page-link'>Sonraki</span></li>";
        }
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }

    /**
     * Get theme configuration
     * 
     * @return array
     */
    public function getThemeConfig(): array
    {
        return $this->themeConfig;
    }

    /**
     * Get active languages (public accessor)
     * 
     * @return array
     */
    public function getActiveLanguages(): array
    {
        return $this->activeLanguages;
    }
}