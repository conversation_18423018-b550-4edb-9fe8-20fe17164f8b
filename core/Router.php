<?php

/**
 * Dynamic router for multi-tenant system
 * Handles URL routing with theme-based dynamic routes
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class Router
{
    private $routes = [];
    private $middleware;
    private $themeConfig;

    public function __construct(TenantMiddleware $middleware)
    {
        $this->middleware = $middleware;
        $this->themeConfig = $middleware->getThemeConfig();
        
        $this->registerRoutes();
    }

    /**
     * Register all routes (common + theme-specific)
     */
    private function registerRoutes(): void
    {
        // Common routes for all themes
        $this->registerCommonRoutes();
        
        // Theme-specific routes
        if ($this->themeConfig) {
            $this->registerThemeRoutes();
        }
        
        // Admin routes
        $this->registerAdminRoutes();
    }

    /**
     * Register common routes available in all themes
     */
    private function registerCommonRoutes(): void
    {
        // Homepage
        $this->get('/', 'HomeController@index');
        
        // Services
        $this->get('/services', 'ServicesController@index');
        $this->get('/services/{slug}', 'ServicesController@show');
        $this->get('/services/category/{category}', 'ServicesController@category');
        
        // Pages
        $this->get('/page/{slug}', 'PageController@show');
        
        // Blog
        $this->get('/blog', 'BlogController@index');
        $this->get('/blog/{slug}', 'BlogController@show');
        
        // Contact
        $this->get('/contact', 'ContactController@index');
        $this->post('/contact', 'ContactController@send');
        
        // Search
        $this->get('/search', 'SearchController@index');
        $this->post('/search', 'SearchController@results');
    }

    /**
     * Register theme-specific routes based on theme configuration
     */
    private function registerThemeRoutes(): void
    {
        if (!isset($this->themeConfig['admin_modules'])) {
            return;
        }

        foreach ($this->themeConfig['admin_modules'] as $moduleKey => $moduleConfig) {
            $controllerName = ucfirst($moduleKey) . 'Controller';
            
            if (isset($moduleConfig['single_record']) && $moduleConfig['single_record']) {
                // Single record modules (like about page)
                $this->get("/{$moduleKey}", "{$controllerName}@show");
            } else {
                // List and detail modules
                $this->get("/{$moduleKey}", "{$controllerName}@index");
                $this->get("/{$moduleKey}/{slug}", "{$controllerName}@show");
                
                // Category routes if applicable
                if (isset($moduleConfig['has_categories']) && $moduleConfig['has_categories']) {
                    $this->get("/{$moduleKey}/category/{category}", "{$controllerName}@category");
                }
            }
        }
    }

    /**
     * Register admin routes
     */
    private function registerAdminRoutes(): void
    {
        $config = require __DIR__ . '/../config/app.php';
        $adminPath = $config['admin_path'];
        
        // Admin authentication
        $this->get("{$adminPath}/login", 'AdminAuthController@loginForm');
        $this->post("{$adminPath}/login", 'AdminAuthController@login');
        $this->get("{$adminPath}/logout", 'AdminAuthController@logout');
        
        // Admin dashboard
        $this->get("{$adminPath}", 'AdminController@dashboard');
        
        // Common admin routes
        $this->get("{$adminPath}/pages", 'AdminPageController@index');
        $this->get("{$adminPath}/pages/create", 'AdminPageController@create');
        $this->post("{$adminPath}/pages", 'AdminPageController@store');
        $this->get("{$adminPath}/pages/{id}/edit", 'AdminPageController@edit');
        $this->post("{$adminPath}/pages/{id}", 'AdminPageController@update');
        $this->post("{$adminPath}/pages/{id}/delete", 'AdminPageController@delete');
        
        $this->get("{$adminPath}/blog", 'AdminBlogController@index');
        $this->get("{$adminPath}/blog/create", 'AdminBlogController@create');
        $this->post("{$adminPath}/blog", 'AdminBlogController@store');
        $this->get("{$adminPath}/blog/{id}/edit", 'AdminBlogController@edit');
        $this->post("{$adminPath}/blog/{id}", 'AdminBlogController@update');
        $this->post("{$adminPath}/blog/{id}/delete", 'AdminBlogController@delete');
        
        $this->get("{$adminPath}/settings", 'AdminSettingsController@index');
        $this->post("{$adminPath}/settings", 'AdminSettingsController@update');
        
        $this->get("{$adminPath}/media", 'AdminMediaController@index');
        $this->post("{$adminPath}/media/upload", 'AdminMediaController@upload');
        $this->post("{$adminPath}/media/{id}/delete", 'AdminMediaController@delete');
        
        // Theme-specific admin routes
        if ($this->themeConfig && isset($this->themeConfig['admin_modules'])) {
            foreach ($this->themeConfig['admin_modules'] as $moduleKey => $moduleConfig) {
                $controllerName = 'Admin' . ucfirst($moduleKey) . 'Controller';
                
                $this->get("{$adminPath}/{$moduleKey}", "{$controllerName}@index");
                $this->get("{$adminPath}/{$moduleKey}/create", "{$controllerName}@create");
                $this->post("{$adminPath}/{$moduleKey}", "{$controllerName}@store");
                $this->get("{$adminPath}/{$moduleKey}/{id}/edit", "{$controllerName}@edit");
                $this->post("{$adminPath}/{$moduleKey}/{id}", "{$controllerName}@update");
                $this->post("{$adminPath}/{$moduleKey}/{id}/delete", "{$controllerName}@delete");
            }
        }
        
        // Central admin routes
        $centralPath = $config['central_admin_path'];
        $this->get("{$centralPath}/login", 'CentralAdminAuthController@loginForm');
        $this->post("{$centralPath}/login", 'CentralAdminAuthController@login');
        $this->get("{$centralPath}/logout", 'CentralAdminAuthController@logout');
        
        $this->get("{$centralPath}", 'CentralAdminController@dashboard');
        $this->get("{$centralPath}/dashboard", 'CentralAdminController@dashboard');
        $this->get("{$centralPath}/index", 'CentralAdminController@dashboard');
        
        $this->get("{$centralPath}/tenants", 'CentralAdminTenantController@index');
        $this->get("{$centralPath}/tenants/create", 'CentralAdminTenantController@create');
        $this->post("{$centralPath}/tenants", 'CentralAdminTenantController@store');
        $this->get("{$centralPath}/tenants/{id}/edit", 'CentralAdminTenantController@edit');
        $this->post("{$centralPath}/tenants/{id}", 'CentralAdminTenantController@update');
        $this->post("{$centralPath}/tenants/{id}/delete", 'CentralAdminTenantController@delete');
        
        $this->get("{$centralPath}/themes", 'ThemesController@index');
        $this->get("{$centralPath}/themes/create", 'ThemesController@create');
        $this->post("{$centralPath}/themes/store", 'ThemesController@store');
    }

    /**
     * Add GET route
     * 
     * @param string $uri
     * @param string $controller
     */
    public function get(string $uri, string $controller): void
    {
        $this->routes['GET'][$uri] = $controller;
    }

    /**
     * Add POST route
     * 
     * @param string $uri
     * @param string $controller
     */
    public function post(string $uri, string $controller): void
    {
        $this->routes['POST'][$uri] = $controller;
    }

    /**
     * Add PUT route
     * 
     * @param string $uri
     * @param string $controller
     */
    public function put(string $uri, string $controller): void
    {
        $this->routes['PUT'][$uri] = $controller;
    }

    /**
     * Add DELETE route
     * 
     * @param string $uri
     * @param string $controller
     */
    public function delete(string $uri, string $controller): void
    {
        $this->routes['DELETE'][$uri] = $controller;
    }

    /**
     * Resolve current request to controller and method
     * 
     * @param string $uri
     * @param string $method
     * @return mixed
     * @throws Exception
     */
    public function resolve(string $uri, string $method = 'GET')
    {
        // Clean URI
        $uri = $this->cleanUri($uri);
        
        // Check for exact match first
        if (isset($this->routes[$method][$uri])) {
            return $this->callController($this->routes[$method][$uri], []);
        }
        
        // Check for parameterized routes
        foreach ($this->routes[$method] as $route => $controller) {
            $params = $this->matchRoute($route, $uri);
            if ($params !== false) {
                return $this->callController($controller, $params);
            }
        }
        
        // Route not found
        throw new Exception("Route not found: {$method} {$uri}", 404);
    }

    /**
     * Clean URI by removing query string and trailing slashes
     * 
     * @param string $uri
     * @return string
     */
    private function cleanUri(string $uri): string
    {
        // Remove query string
        $uri = strtok($uri, '?');
        
        // Remove trailing slash except for root
        if ($uri !== '/' && substr($uri, -1) === '/') {
            $uri = rtrim($uri, '/');
        }
        
        return $uri;
    }

    /**
     * Match route pattern with URI and extract parameters
     * 
     * @param string $route
     * @param string $uri
     * @return array|false
     */
    private function matchRoute(string $route, string $uri)
    {
        // Convert route pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $uri, $matches)) {
            array_shift($matches); // Remove full match
            
            // Extract parameter names from route
            preg_match_all('/\{([^}]+)\}/', $route, $paramNames);
            $paramNames = $paramNames[1];
            
            // Combine parameter names with values
            $params = [];
            foreach ($paramNames as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }
            
            return $params;
        }
        
        return false;
    }

    /**
     * Call controller method with parameters
     * 
     * @param string $controllerAction
     * @param array $params
     * @return mixed
     * @throws Exception
     */
    private function callController(string $controllerAction, array $params = [])
    {
        [$controllerName, $method] = explode('@', $controllerAction);
        
        // Determine controller path based on request type
        if (defined('IS_CENTRAL_ADMIN') && IS_CENTRAL_ADMIN) {
            $controllerPath = __DIR__ . "/../admin/central/controllers/{$controllerName}.php";
        } elseif ($this->middleware->isAdminRequest()) {
            $controllerPath = __DIR__ . "/../admin/tenant/controllers/{$controllerName}.php";
        } else {
            // Frontend controllers are in themes for display only
            $themeName = defined('THEME_NAME') ? THEME_NAME : 'lawyer1';
            $controllerPath = __DIR__ . "/../themes/{$themeName}/controllers/{$controllerName}.php";
        }
        
        if (!file_exists($controllerPath)) {
            throw new Exception("Controller not found: {$controllerName}");
        }
        
        require_once $controllerPath;
        
        if (!class_exists($controllerName)) {
            throw new Exception("Controller class not found: {$controllerName}");
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $method)) {
            throw new Exception("Method not found: {$controllerName}@{$method}");
        }
        
        // Pass parameters to controller method
        return call_user_func_array([$controller, $method], $params);
    }

    /**
     * Generate URL for given route
     * 
     * @param string $route
     * @param array $params
     * @param array $query
     * @return string
     */
    public function url(string $route, array $params = [], array $query = []): string
    {
        $url = $route;
        
        // Replace parameters in route
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }
        
        // Add query parameters
        if (!empty($query)) {
            $url .= '?' . http_build_query($query);
        }
        
        return $url;
    }

    

    /**
     * Get all registered routes
     * 
     * @return array
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }

    /**
     * Check if route exists
     * 
     * @param string $method
     * @param string $uri
     * @return bool
     */
    public function routeExists(string $method, string $uri): bool
    {
        $uri = $this->cleanUri($uri);
        
        if (isset($this->routes[$method][$uri])) {
            return true;
        }
        
        foreach ($this->routes[$method] as $route => $controller) {
            if ($this->matchRoute($route, $uri) !== false) {
                return true;
            }
        }
        
        return false;
    }
}