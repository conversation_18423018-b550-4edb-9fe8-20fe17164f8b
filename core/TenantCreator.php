<?php

/**
 * Tenant creation and management system
 * Handles tenant database operations only
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class TenantCreator
{
    private $database;

    public function __construct()
    {
        $this->database = new Database();
    }

    /**
     * Create new tenant - only database operations
     * 
     * @param array|string $data - Array with tenant data or domain string for backward compatibility
     * @param int|null $themeId
     * @param string|null $expiredAt
     * @return array
     * @throws Exception
     */
    public function createTenant(
        $data, 
        ?int $themeId = null, 
        ?string $expiredAt = null
    ): array {
        // Handle both array and string parameters for backward compatibility
        if (is_array($data)) {
            $domain = $data['domain'];
            $theme = $data['theme'] ?? null;
            $status = $data['status'] ?? 'active';
            
            // Convert theme name to theme_id
            if ($theme && !$themeId) {
                $themeId = $this->getThemeIdByName($theme);
            }
        } else {
            $domain = $data;
            $status = 'active';
        }
        
        try {
            $this->database->beginTransaction('central');
            
            // 1. Validate domain
            if (!$this->validateDomain($domain)) {
                throw new Exception("Invalid domain format: {$domain}");
            }
            
            // 2. Check if domain already exists
            if ($this->domainExists($domain)) {
                throw new Exception("Domain already exists: {$domain}");
            }
            
            // 3. Get theme information
            if (!$themeId) {
                throw new Exception("Tema ID bulunamadı");
            }
            
            $theme = $this->getTheme($themeId);
            if (!$theme) {
                throw new Exception("Seçilen tema bulunamadı: {$themeId}");
            }
            
            // 4. Create tenant record in central database
            $tenantId = $this->database->insertCentral('tenants', [
                'domain' => $domain,
                'theme_id' => $themeId,
                'status' => $status,
                'expired_at' => $expiredAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->database->commit('central');
            
            return [
                'tenant_id' => $tenantId,
                'domain' => $domain,
                'theme' => $theme['name'],
                'status' => $status
            ];
            
        } catch (Exception $e) {
            $this->database->rollback('central');
            throw $e;
        }
    }

    /**
     * Validate domain format
     * 
     * @param string $domain
     * @return bool
     */
    private function validateDomain(string $domain): bool
    {
        return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) !== false;
    }

    /**
     * Check if domain already exists
     * 
     * @param string $domain
     * @return bool
     */
    private function domainExists(string $domain): bool
    {
        $query = "SELECT id FROM tenants WHERE domain = ?";
        $stmt = $this->database->queryCentral($query, [$domain]);
        return $stmt->fetch() !== false;
    }

    /**
     * Get theme information
     * 
     * @param int $themeId
     * @return array|null
     */
    private function getTheme(int $themeId): ?array
    {
        $themes = $this->getAvailableThemes();
        foreach ($themes as $theme) {
            if ($theme['id'] == $themeId) {
                return $theme;
            }
        }
        return null;
    }

    /**
     * Delete tenant - only database operations
     * 
     * @param string $domain
     * @return bool
     * @throws Exception
     */
    public function deleteTenant(string $domain): bool
    {
        try {
            $this->database->beginTransaction('central');
            
            // Get tenant info
            $query = "SELECT * FROM tenants WHERE domain = ?";
            $stmt = $this->database->queryCentral($query, [$domain]);
            $tenant = $stmt->fetch();
            
            if (!$tenant) {
                throw new Exception("Tenant not found: {$domain}");
            }
            
            // Delete tenant record
            $this->database->deleteCentral('tenants', ['domain' => $domain]);
            
            $this->database->commit('central');
            
            return true;
            
        } catch (Exception $e) {
            $this->database->rollback('central');
            throw $e;
        }
    }

    /**
     * Get all tenants
     * 
     * @return array
     */
    public function getAllTenants(): array
    {
        $query = "
            SELECT t.*, th.name as theme_name 
            FROM tenants t 
            LEFT JOIN themes th ON t.theme_id = th.id 
            ORDER BY t.created_at DESC
        ";
        $stmt = $this->database->queryCentral($query);
        return $stmt->fetchAll();
    }

    /**
     * Get tenant by domain
     * 
     * @param string $domain
     * @return array|null
     */
    public function getTenantByDomain(string $domain): ?array
    {
        $query = "
            SELECT t.*, th.name as theme_name 
            FROM tenants t 
            LEFT JOIN themes th ON t.theme_id = th.id 
            WHERE t.domain = ?
        ";
        $stmt = $this->database->queryCentral($query, [$domain]);
        return $stmt->fetch() ?: null;
    }

    /**
     * Update tenant
     * 
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateTenant(int $id, array $data): bool
    {
        $allowedFields = ['domain', 'theme', 'status', 'expired_at'];
        $updateData = [];
        
        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                if ($key === 'theme') {
                    // Convert theme name to theme_id
                    $themeId = $this->getThemeIdByName($value);
                    if ($themeId) {
                        $updateData['theme_id'] = $themeId;
                    }
                } else {
                    $updateData[$key] = $value;
                }
            }
        }
        
        if (empty($updateData)) {
            return false;
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        $affected = $this->database->updateCentral('tenants', 
            $updateData, 
            ['id' => $id]
        );
        
        return $affected > 0;
    }

    /**
     * Update tenant status
     * 
     * @param string $domain
     * @param string $status
     * @return bool
     */
    public function updateTenantStatus(string $domain, string $status): bool
    {
        $validStatuses = ['active', 'inactive', 'suspended'];
        
        if (!in_array($status, $validStatuses)) {
            throw new Exception("Invalid status: {$status}");
        }
        
        $affected = $this->database->updateCentral('tenants', 
            ['status' => $status], 
            ['domain' => $domain]
        );
        
        return $affected > 0;
    }

    /**
     * Get theme ID by theme name
     * 
     * @param string $themeName
     * @return int|null
     */
    private function getThemeIdByName(string $themeName): ?int
    {
        // Hardcoded theme mapping since themes table might not exist
        $themeMapping = [
            'lawyer1' => 1,
            'dietitian1' => 2,
            'technical1' => 3
        ];
        
        return $themeMapping[$themeName] ?? null;
    }

    /**
     * Get all available themes
     * 
     * @return array
     */
    public function getAvailableThemes(): array
    {
        // Return hardcoded themes since themes table might not exist
        return [
            ['id' => 1, 'name' => 'lawyer1', 'display_name' => 'Avukat Teması'],
            ['id' => 2, 'name' => 'dietitian1', 'display_name' => 'Diyetisyen Teması'],
            ['id' => 3, 'name' => 'technical1', 'display_name' => 'Teknik Servis Teması']
        ];
    }
}