<?php

/**
 * Multi-tenant database connection manager
 * Handles both central and tenant database connections
 * 
 * <AUTHOR> System
 * @version 1.0
 */
class Database
{
    private static $centralConnection = null;
    private static $tenantConnection = null;
    private static $currentTenantDb = null;
    private $config;

    public function __construct()
    {
        $this->config = require __DIR__ . '/../config/database.php';
    }

    /**
     * Get central database connection
     * 
     * @return PDO
     * @throws Exception
     */
    public function getCentralConnection(): PDO
    {
        if (self::$centralConnection === null) {
            $config = $this->config['central'];
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
            
            try {
                self::$centralConnection = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            } catch (PDOException $e) {
                throw new Exception("Central database connection failed: " . $e->getMessage());
            }
        }

        return self::$centralConnection;
    }

    /**
     * Get tenant database connection
     * 
     * @param string $tenantDbName
     * @return PDO
     * @throws Exception
     */
    public function getTenantConnection(string $tenantDbName): PDO
    {
        if (self::$tenantConnection === null || self::$currentTenantDb !== $tenantDbName) {
            $config = $this->config['tenant'];
            $dsn = "mysql:host={$config['host']};dbname={$tenantDbName};charset={$config['charset']}";
            
            try {
                self::$tenantConnection = new PDO($dsn, $config['username'], $config['password'], $config['options']);
                self::$currentTenantDb = $tenantDbName;
            } catch (PDOException $e) {
                throw new Exception("Tenant database connection failed: " . $e->getMessage());
            }
        }

        return self::$tenantConnection;
    }

    /**
     * Get tenant database connection with specific config
     * 
     * @param array $tenantConfig
     * @return PDO
     * @throws Exception
     */
    public function getTenantConnectionWithConfig(array $tenantConfig): PDO
    {
        $tenantDbName = $tenantConfig['database'];
        
        if (self::$tenantConnection === null || self::$currentTenantDb !== $tenantDbName) {
            $dsn = "mysql:host={$tenantConfig['host']};dbname={$tenantDbName};charset={$tenantConfig['charset']}";
            
            try {
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];
                self::$tenantConnection = new PDO($dsn, $tenantConfig['username'], $tenantConfig['password'], $options);
                self::$currentTenantDb = $tenantDbName;
            } catch (PDOException $e) {
                throw new Exception("Tenant database connection failed: " . $e->getMessage());
            }
        }

        return self::$tenantConnection;
    }

    /**
     * Execute query on central database
     * 
     * @param string $query
     * @param array $params
     * @return PDOStatement
     */
    public function queryCentral(string $query, array $params = []): PDOStatement
    {
        $connection = $this->getCentralConnection();
        $stmt = $connection->prepare($query);
        $stmt->execute($params);
        return $stmt;
    }

    /**
     * Execute query on tenant database
     * 
     * @param string $tenantDbName
     * @param string $query
     * @param array $params
     * @return PDOStatement
     */
    public function queryTenant(string $tenantDbName, string $query, array $params = []): PDOStatement
    {
        $connection = $this->getTenantConnection($tenantDbName);
        $stmt = $connection->prepare($query);
        $stmt->execute($params);
        return $stmt;
    }

    /**
     * Insert data into central database
     * 
     * @param string $table
     * @param array $data
     * @return int Last insert ID
     */
    public function insertCentral(string $table, array $data): int
    {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->queryCentral($query, $data);
        
        return $this->getCentralConnection()->lastInsertId();
    }

    /**
     * Insert data into tenant database
     * 
     * @param string $tenantDbName
     * @param string $table
     * @param array $data
     * @return int Last insert ID
     */
    public function insertTenant(string $tenantDbName, string $table, array $data): int
    {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->queryTenant($tenantDbName, $query, $data);
        
        return $this->getTenantConnection($tenantDbName)->lastInsertId();
    }

    /**
     * Update data in central database
     * 
     * @param string $table
     * @param array $data
     * @param array $where
     * @return int Affected rows
     */
    public function updateCentral(string $table, array $data, array $where): int
    {
        $setClause = [];
        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        
        $whereClause = [];
        foreach ($where as $key => $value) {
            $whereClause[] = "{$key} = :where_{$key}";
            $data["where_{$key}"] = $value;
        }
        
        $query = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->queryCentral($query, $data);
        
        return $stmt->rowCount();
    }

    /**
     * Update data in tenant database
     * 
     * @param string $tenantDbName
     * @param string $table
     * @param array $data
     * @param array $where
     * @return int Affected rows
     */
    public function updateTenant(string $tenantDbName, string $table, array $data, array $where): int
    {
        $setClause = [];
        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        
        $whereClause = [];
        foreach ($where as $key => $value) {
            $whereClause[] = "{$key} = :where_{$key}";
            $data["where_{$key}"] = $value;
        }
        
        $query = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->queryTenant($tenantDbName, $query, $data);
        
        return $stmt->rowCount();
    }

    /**
     * Delete data from central database
     * 
     * @param string $table
     * @param array $where
     * @return int Affected rows
     */
    public function deleteCentral(string $table, array $where): int
    {
        $whereClause = [];
        foreach ($where as $key => $value) {
            $whereClause[] = "{$key} = :{$key}";
        }
        
        $query = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->queryCentral($query, $where);
        
        return $stmt->rowCount();
    }

    /**
     * Delete data from tenant database
     * 
     * @param string $tenantDbName
     * @param string $table
     * @param array $where
     * @return int Affected rows
     */
    public function deleteTenant(string $tenantDbName, string $table, array $where): int
    {
        $whereClause = [];
        foreach ($where as $key => $value) {
            $whereClause[] = "{$key} = :{$key}";
        }
        
        $query = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->queryTenant($tenantDbName, $query, $where);
        
        return $stmt->rowCount();
    }

    /**
     * Create tenant database
     * 
     * @param string $tenantDbName
     * @return bool
     */
    public function createTenantDatabase(string $tenantDbName): bool
    {
        try {
            $config = $this->config['tenant'];
            $dsn = "mysql:host={$config['host']};charset={$config['charset']}";
            $pdo = new PDO($dsn, $config['username'], $config['password']);
            
            $query = "CREATE DATABASE IF NOT EXISTS `{$tenantDbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($query);
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("Failed to create tenant database: " . $e->getMessage());
        }
    }

    /**
     * Run SQL migration file on tenant database
     * 
     * @param string $tenantDbName
     * @param string $migrationFile
     * @return bool
     */
    public function runMigration(string $tenantDbName, string $migrationFile): bool
    {
        try {
            $migrationPath = __DIR__ . "/../migrations/{$migrationFile}";
            
            if (!file_exists($migrationPath)) {
                throw new Exception("Migration file not found: {$migrationFile}");
            }
            
            $sql = file_get_contents($migrationPath);
            $connection = $this->getTenantConnection($tenantDbName);
            
            // Split SQL by semicolons and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $connection->exec($statement);
                }
            }
            
            return true;
        } catch (Exception $e) {
            throw new Exception("Migration failed: " . $e->getMessage());
        }
    }

    /**
     * Begin transaction
     * 
     * @param string $type 'central' or tenant db name
     * @return bool
     */
    public function beginTransaction(string $type = 'central'): bool
    {
        if ($type === 'central') {
            return $this->getCentralConnection()->beginTransaction();
        } else {
            return $this->getTenantConnection($type)->beginTransaction();
        }
    }

    /**
     * Commit transaction
     * 
     * @param string $type 'central' or tenant db name
     * @return bool
     */
    public function commit(string $type = 'central'): bool
    {
        if ($type === 'central') {
            return $this->getCentralConnection()->commit();
        } else {
            return $this->getTenantConnection($type)->commit();
        }
    }

    /**
     * Rollback transaction
     * 
     * @param string $type 'central' or tenant db name
     * @return bool
     */
    public function rollback(string $type = 'central'): bool
    {
        if ($type === 'central') {
            return $this->getCentralConnection()->rollback();
        } else {
            return $this->getTenantConnection($type)->rollback();
        }
    }
}