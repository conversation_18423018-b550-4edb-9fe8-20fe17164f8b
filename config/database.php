<?php

/**
 * Database configuration for multi-tenant system
 * 
 * <AUTHOR> System
 * @version 1.0
 */

return [
    // Central database configuration
    'central' => [
        'host' => $_ENV['CENTRAL_DB_HOST'] ?? 'localhost',
        'database' => $_ENV['CENTRAL_DB_NAME'] ?? 'central_db',
        'username' => $_ENV['CENTRAL_DB_USER'] ?? 'root',
        'password' => $_ENV['CENTRAL_DB_PASS'] ?? '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],
    
    // Tenant database configuration template
    'tenant' => [
        'host' => $_ENV['TENANT_DB_HOST'] ?? 'localhost',
        'username' => $_ENV['TENANT_DB_USER'] ?? 'root',
        'password' => $_ENV['TENANT_DB_PASS'] ?? '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ]
];