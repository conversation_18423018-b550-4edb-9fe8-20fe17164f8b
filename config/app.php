<?php

/**
 * Application configuration
 * 
 * <AUTHOR> System
 * @version 1.0
 */

return [
    // Application settings
    'app_name' => 'Multi-Tenant SaaS',
    'app_version' => '1.0.0',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'timezone' => 'Europe/Istanbul',
    
    // Security settings
    'session_name' => 'MULTITENANT_SESSION',
    'csrf_token_name' => '_token',
    'password_hash_algo' => PASSWORD_DEFAULT,
    
    // File upload settings
    'upload_path' => 'public/assets/uploads/',
    'max_file_size' => 5 * 1024 * 1024, // 5MB
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    
    // Cache settings
    'cache_enabled' => true,
    'cache_ttl' => 3600, // 1 hour
    
    // Language settings (DISABLED - Single language only)
    'default_language' => 'tr',
    
    // Theme settings
    'themes_path' => 'themes/',
    'default_theme' => 'default',
    
    // Admin settings
    'admin_path' => '/admin',
    'central_admin_path' => '/central',
    
    // URL settings
    'base_url' => $_ENV['BASE_URL'] ?? 'http://localhost',
    'force_https' => $_ENV['FORCE_HTTPS'] ?? false,
];